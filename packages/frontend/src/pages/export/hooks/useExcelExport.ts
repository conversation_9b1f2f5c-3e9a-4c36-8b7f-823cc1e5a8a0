/**
 * Excel Export Hook
 * Handles Excel/XLSX export functionality with lazy loading
 */

import { format } from "date-fns";
import { toast } from "sonner";
import logger from "@/utils/logging/unifiedLogger";
import type { ProjectImage } from "@/types";
import { calculateMetrics } from "@spheroseg/shared";
import { loadXLSX } from "@/utils/lazyImports";

export interface ExcelExportOptions {
  includeMetadata?: boolean;
  includeStatistics?: boolean;
  separateSheets?: boolean;
}

export function useExcelExport() {
  /**
   * Export data to Excel
   */
  const exportToExcel = async (
    images: ProjectImage[],
    projectName: string,
    options: ExcelExportOptions = {},
  ) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Loading Excel library...");
      
      // Load XLSX library dynamically
      const { utils, writeFile } = await loadXLSX();
      
      toast.dismiss(loadingToast);
      toast.loading("Preparing Excel export...");
      
      const workbook = utils.book_new();

      // Main data sheet
      const mainData = images.map((image) => {
        const metrics = image.segmentation?.polygons
          ? calculateMetrics(image.segmentation.polygons)
          : null;

        return {
          "Image ID": image.id,
          Filename: image.filename,
          "Upload Date": format(new Date(image.uploadDate), "yyyy-MM-dd HH:mm"),
          Status: image.segmentationStatus,
          "Cell Count": metrics?.cellCount || 0,
          "Average Area": metrics?.avgArea?.toFixed(2) || 0,
          "Total Area": metrics?.totalArea?.toFixed(2) || 0,
          "Min Area": metrics?.minArea?.toFixed(2) || 0,
          "Max Area": metrics?.maxArea?.toFixed(2) || 0,
        };
      });

      const mainSheet = utils.json_to_sheet(mainData);
      utils.book_append_sheet(workbook, mainSheet, "Images");

      // Statistics sheet (if requested)
      if (options.includeStatistics) {
        const stats = {
          "Total Images": images.length,
          Processed: images.filter((i) => i.segmentationStatus === "completed")
            .length,
          Pending: images.filter((i) => i.segmentationStatus === "pending")
            .length,
          Failed: images.filter((i) => i.segmentationStatus === "failed")
            .length,
          "Export Date": format(new Date(), "yyyy-MM-dd HH:mm"),
        };

        const statsSheet = utils.json_to_sheet([stats]);
        utils.book_append_sheet(workbook, statsSheet, "Statistics");
      }

      // Generate filename
      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_export_${timestamp}.xlsx`;

      // Write file
      writeFile(workbook, filename);

      toast.success(`Exported ${images.length} images to Excel`);
      logger.info("Excel export completed", { imageCount: images.length });
    } catch (error) {
      logger.error("Excel export failed:", error);
      toast.error("Failed to export Excel file");
      throw error;
    }
  };

  /**
   * Export metrics to Excel
   */
  const exportMetricsToExcel = async (metrics: any[], projectName: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Loading Excel library...");
      
      // Load XLSX library dynamically
      const { utils, writeFile } = await loadXLSX();
      
      toast.dismiss(loadingToast);
      toast.loading("Preparing metrics export...");
      
      const workbook = utils.book_new();
      const sheet = utils.json_to_sheet(metrics);
      utils.book_append_sheet(workbook, sheet, "Metrics");

      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_metrics_${timestamp}.xlsx`;

      writeFile(workbook, filename);

      toast.success("Metrics exported to Excel");
      logger.info("Metrics Excel export completed");
    } catch (error) {
      logger.error("Metrics Excel export failed:", error);
      toast.error("Failed to export metrics");
      throw error;
    }
  };

  return {
    exportToExcel,
    exportMetricsToExcel,
  };
}
