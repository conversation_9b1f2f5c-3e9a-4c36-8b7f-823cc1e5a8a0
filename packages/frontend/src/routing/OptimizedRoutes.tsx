/**
 * Optimized Route Configuration with Lazy Loading
 * Reduces initial bundle by implementing route-based code splitting
 */

import { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

// Lazy load all route components
const LazyRoutes = {
  // Public routes (small, load immediately)
  SignIn: lazy(() => import('../pages/SignIn')),
  SignUp: lazy(() => import('../pages/SignUp')),
  ForgotPassword: lazy(() => import('../pages/ForgotPassword')),
  VerifyEmail: lazy(() => import('../pages/VerifyEmail')),
  AcceptInvitation: lazy(() => import('../pages/AcceptInvitation')),
  RequestAccess: lazy(() => import('../pages/RequestAccess')),
  
  // Main app routes (load on demand)
  Dashboard: lazy(() => 
    import(/* webpackChunkName: "dashboard" */ '../pages/Dashboard')
  ),
  ProjectDetail: lazy(() => 
    import(/* webpackChunkName: "project" */ '../pages/ProjectDetail')
  ),
  Settings: lazy(() => 
    import(/* webpackChunkName: "settings" */ '../pages/Settings')
  ),
  Profile: lazy(() => 
    import(/* webpackChunkName: "profile" */ '../pages/Profile')
  ),
  
  // Heavy feature routes (defer loading)
  Segmentation: lazy(() => 
    import(/* webpackChunkName: "segmentation" */ '../pages/segmentation/SegmentationPage')
  ),
  Export: lazy(() => 
    import(/* webpackChunkName: "export" */ '../pages/export/ExportPage')
  ),
  
  // Static pages (low priority)
  About: lazy(() => import('../pages/AboutPage')),
  Documentation: lazy(() => import('../pages/Documentation')),
  TermsOfService: lazy(() => import('../pages/TermsOfService')),
  PrivacyPolicy: lazy(() => import('../pages/PrivacyPolicy')),
  
  // Error pages
  NotFound: lazy(() => import('../pages/NotFound')),
  ServerError: lazy(() => import('../pages/ServerError')),
};

// Prefetch priority routes
const PRIORITY_ROUTES = ['Dashboard', 'ProjectDetail'];

// Loading component with better UX
const RouteLoadingFallback = () => (
  <div className="flex h-screen items-center justify-center">
    <div className="text-center">
      <LoadingSpinner className="mx-auto mb-4" />
      <p className="text-sm text-muted-foreground">Loading...</p>
    </div>
  </div>
);

// Error fallback component
const RouteErrorFallback = ({ error, resetError }: any) => (
  <div className="flex h-screen items-center justify-center">
    <div className="text-center max-w-md">
      <h2 className="text-xl font-semibold mb-2">Failed to load page</h2>
      <p className="text-sm text-muted-foreground mb-4">
        {error?.message || 'An unexpected error occurred'}
      </p>
      <button 
        onClick={resetError}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
      >
        Try Again
      </button>
    </div>
  </div>
);

/**
 * Optimized route configuration
 */
export const OptimizedRoutes = () => {
  // Prefetch priority routes on mount
  useEffect(() => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        PRIORITY_ROUTES.forEach(route => {
          const Component = LazyRoutes[route as keyof typeof LazyRoutes];
          if (Component) {
            Component.preload?.();
          }
        });
      });
    }
  }, []);
  
  return (
    <ErrorBoundary fallback={RouteErrorFallback}>
      <Suspense fallback={<RouteLoadingFallback />}>
        <Routes>
          {/* Public Routes */}
          <Route path="/signin" element={<LazyRoutes.SignIn />} />
          <Route path="/signup" element={<LazyRoutes.SignUp />} />
          <Route path="/forgot-password" element={<LazyRoutes.ForgotPassword />} />
          <Route path="/verify-email" element={<LazyRoutes.VerifyEmail />} />
          <Route path="/accept-invitation/:token" element={<LazyRoutes.AcceptInvitation />} />
          <Route path="/request-access" element={<LazyRoutes.RequestAccess />} />
          
          {/* Protected Routes */}
          <Route path="/" element={<ProtectedRoute />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<LazyRoutes.Dashboard />} />
            <Route path="projects/:id" element={<LazyRoutes.ProjectDetail />} />
            <Route path="projects/:id/segment" element={<LazyRoutes.Segmentation />} />
            <Route path="projects/:id/export" element={<LazyRoutes.Export />} />
            <Route path="settings" element={<LazyRoutes.Settings />} />
            <Route path="profile" element={<LazyRoutes.Profile />} />
          </Route>
          
          {/* Static Pages */}
          <Route path="/about" element={<LazyRoutes.About />} />
          <Route path="/docs" element={<LazyRoutes.Documentation />} />
          <Route path="/terms" element={<LazyRoutes.TermsOfService />} />
          <Route path="/privacy" element={<LazyRoutes.PrivacyPolicy />} />
          
          {/* Error Routes */}
          <Route path="/500" element={<LazyRoutes.ServerError />} />
          <Route path="*" element={<LazyRoutes.NotFound />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

/**
 * Route prefetching utility
 */
export const prefetchRoute = (routeName: keyof typeof LazyRoutes) => {
  const Component = LazyRoutes[routeName];
  if (Component && 'preload' in Component) {
    Component.preload();
  }
};

/**
 * Batch prefetch routes
 */
export const prefetchRoutes = (routeNames: Array<keyof typeof LazyRoutes>) => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      routeNames.forEach(prefetchRoute);
    });
  } else {
    setTimeout(() => {
      routeNames.forEach(prefetchRoute);
    }, 1000);
  }
};

// Hook for route prefetching
export const useRoutePrefetch = (routeName: keyof typeof LazyRoutes) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      prefetchRoute(routeName);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [routeName]);
};

// Export for use in navigation components
export { LazyRoutes };