/**
 * MIGRATION NOTICE: Client-side segmentation logic moved to experimental folder
 * 
 * The simulation functions (segmentImage, applyThresholding, findContours) have been
 * moved to experimental/client-segmentation/ as they are unused in production.
 * 
 * All real segmentation happens on the backend ML service.
 * This file now only provides type re-exports for backwards compatibility.
 */

// Re-export types from shared package (SSOT)
export type { Point, Polygon, SegmentationResultData } from "@spheroseg/shared";
export type { SegmentationResultData as SegmentationResult } from "@spheroseg/shared";

// DEPRECATED: Moved to experimental/client-segmentation/clientSegmentation.ts
// This was unused simulation code - real segmentation uses backend ML service

// DEPRECATED: Moved to experimental/client-segmentation/clientSegmentation.ts
// This was unused simulation code - real segmentation uses backend ML service

// DEPRECATED: Moved to experimental/client-segmentation/clientSegmentation.ts
// This was unused simulation code - real segmentation uses backend ML service

// DUPLICATE FUNCTIONS REMOVED - Use shared package instead:
// import { calculatePolygonArea, calculatePerimeter } from '@spheroseg/shared';
// 
// These functions were duplicating the canonical implementations in the shared package.
// All code should use the SSOT from @spheroseg/shared to avoid inconsistencies.

// Re-export from shared package for backwards compatibility
export { calculatePolygonArea, calculatePerimeter } from '@spheroseg/shared';
