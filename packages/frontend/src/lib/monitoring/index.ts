export * from "./performanceMonitoring";
export * from "./usePerformanceMonitoring";

// Import unified performance monitor
import { unifiedPerformanceMonitor, UnifiedPerformanceMonitor } from '@/utils/performance/UnifiedPerformanceMonitor';
import { createPerformanceMonitoring } from "./performanceMonitoring";

// Export the unified monitor as the primary instance
export const performanceMonitoring = unifiedPerformanceMonitor;
export const unifiedMonitor = unifiedPerformanceMonitor;
export { UnifiedPerformanceMonitor };

// Legacy export for backward compatibility
/**
 * @deprecated Use performanceMonitoring (which is now the unified monitor) or unifiedMonitor directly
 */
export const legacyPerformanceMonitoring = createPerformanceMonitoring({
  enabled:
    import.meta.env.PROD || import.meta.env.VITE_ENABLE_MONITORING === "true",
  endpoint: `${import.meta.env.VITE_API_BASE_URL || ""}/api/metrics`,
});
