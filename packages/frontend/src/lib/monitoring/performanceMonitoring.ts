// Re-export types from the unified monitor
import { 
  UnifiedPerformanceMonitor, 
  unifiedPerformanceMonitor, 
  PerformanceMonitoringOptions,
  MetricType,
  MetricTypeEnum,
  PerformanceMetric
} from '@/utils/performance/UnifiedPerformanceMonitor';

// Legacy interface - maintained for backward compatibility
/**
 * @deprecated Use UnifiedPerformanceMonitor instead
 */
export interface PerformanceMonitoring {
  startTimer: (name: string) => void;
  endTimer: (name: string) => void;
  recordMetric: (metric: any) => void;
}

// Re-export types for backward compatibility
export { PerformanceMonitoringOptions, MetricType, MetricTypeEnum };
export const MetricType_Compat = MetricTypeEnum; // Alternative export name
export type PageLoadMetric = PerformanceMetric;
export type ComponentRenderMetric = PerformanceMetric;
export type ApiRequestMetric = PerformanceMetric;
export type ResourceLoadMetric = PerformanceMetric;
export type UserInteractionMetric = PerformanceMetric;
export type MemoryUsageMetric = PerformanceMetric;

import apiClient from "@/services/api/client";

/**
 * Frontend implementation of performance monitoring
 * @deprecated This class now serves as a facade to UnifiedPerformanceMonitor. Use UnifiedPerformanceMonitor directly for new code.
 */
export class FrontendPerformanceMonitoring {
  public startTimer: (name: string) => void;
  public endTimer: (name: string) => void;
  public recordMetric: (metric: any) => void;
  private static instance: FrontendPerformanceMonitoring | null = null;
  private unifiedMonitor: UnifiedPerformanceMonitor;

  /**
   * Get singleton instance
   * @deprecated Use unifiedPerformanceMonitor directly instead
   */
  public static getInstance(
    options?: Partial<PerformanceMonitoringOptions>,
  ): FrontendPerformanceMonitoring {
    if (!FrontendPerformanceMonitoring.instance) {
      FrontendPerformanceMonitoring.instance =
        new FrontendPerformanceMonitoring(options);
    } else if (options) {
      // Update options on the unified monitor
      FrontendPerformanceMonitoring.instance.unifiedMonitor.updateOptions(options);
    }
    return FrontendPerformanceMonitoring.instance;
  }

  constructor(options: Partial<PerformanceMonitoringOptions> = {}) {
    // Use the unified monitor as the underlying implementation
    this.unifiedMonitor = UnifiedPerformanceMonitor.getInstance(options);

    // Bind facade methods
    this.startTimer = this.startTimerImpl.bind(this);
    this.endTimer = this.endTimerImpl.bind(this);
    this.recordMetric = this.recordMetricImpl.bind(this);
  }

  /**
   * Set up performance observers to automatically collect metrics
   * @deprecated Observers are now handled by UnifiedPerformanceMonitor automatically
   */
  private setupPerformanceObservers(): void {
    // Performance observers are now handled by the UnifiedPerformanceMonitor
    // This method is kept for backward compatibility but does nothing
  }

  /**
   * Record page load metrics
   * @deprecated Page load metrics are automatically recorded by UnifiedPerformanceMonitor
   */
  public recordPageLoadMetrics(): void {
    // Page load metrics are automatically recorded by the UnifiedPerformanceMonitor
    // This method is kept for backward compatibility but delegates to the unified monitor
    if (typeof window !== "undefined" && performance) {
      try {
        const navigationEntry = performance.getEntriesByType(
          "navigation",
        )[0] as PerformanceNavigationTiming;

        if (navigationEntry) {
          const metric: PageLoadMetric = {
            type: MetricTypeEnum.PAGE_LOAD,
            timestamp: Date.now(),
            value: navigationEntry.duration,
            route: window.location.pathname,
            loadTime: navigationEntry.duration,
            domContentLoaded:
              navigationEntry.domContentLoadedEventEnd -
              navigationEntry.startTime,
            firstPaint: 0,
            firstContentfulPaint: 0,
          };

          // Get first paint and first contentful paint
          const paintEntries = performance.getEntriesByType("paint");
          paintEntries.forEach((entry) => {
            if (entry.name === "first-paint") {
              metric.firstPaint = entry.startTime;
            } else if (entry.name === "first-contentful-paint") {
              metric.firstContentfulPaint = entry.startTime;
            }
          });

          this.unifiedMonitor.recordMetric(metric);
        }
      } catch (_e) {}
    }
  }

  /**
   * Record component render time
   * @deprecated Use unifiedPerformanceMonitor.recordComponentRender() instead
   */
  public recordComponentRenderMetric(
    component: string,
    renderTime: number,
  ): void {
    this.unifiedMonitor.recordComponentRender(component, renderTime);
  }

  /**
   * Record API request metric
   * @deprecated Use unifiedPerformanceMonitor.recordApiRequest() instead
   */
  public recordApiRequestMetric(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    error?: string,
  ): void {
    this.unifiedMonitor.recordApiRequest(endpoint, method, duration, status, error);
  }

  /**
   * Record resource load metric
   * @deprecated Resource load metrics are automatically handled by UnifiedPerformanceMonitor
   */
  private recordResourceLoadMetric(entry: PerformanceResourceTiming): void {
    // Resource load metrics are automatically handled by the UnifiedPerformanceMonitor
    // This method is kept for backward compatibility
    const metric: ResourceLoadMetric = {
      type: MetricTypeEnum.RESOURCE_LOAD,
      timestamp: Date.now(),
      value: entry.duration,
      resourceUrl: entry.name,
      resourceType: entry.initiatorType,
      loadTime: entry.duration,
      size: entry.transferSize,
    };

    this.unifiedMonitor.recordMetric(metric);
  }

  /**
   * Record user interaction metric
   * @deprecated Use unifiedPerformanceMonitor.recordUserInteraction() instead
   */
  public recordUserInteractionMetric(
    action: string,
    target: string,
    duration: number,
  ): void {
    this.unifiedMonitor.recordUserInteraction(action, target, duration);
  }

  /**
   * Record memory usage metric
   * @deprecated Memory usage metrics are automatically handled by UnifiedPerformanceMonitor
   */
  private recordMemoryUsageMetric(): void {
    // Memory usage metrics are automatically handled by the UnifiedPerformanceMonitor
    // This method is kept for backward compatibility
    if (typeof window !== "undefined") {
      try {
        const memory = (performance as any).memory;
        if (memory) {
          const metric: MemoryUsageMetric = {
            type: MetricTypeEnum.MEMORY_USAGE,
            timestamp: Date.now(),
            value: memory.usedJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
            totalJSHeapSize: memory.totalJSHeapSize,
            usedJSHeapSize: memory.usedJSHeapSize,
          };

          this.unifiedMonitor.recordMetric(metric);
        }
      } catch (_e) {}
    }
  }

  /**
   * Flush metrics to the server
   * @deprecated Use unifiedPerformanceMonitor.flushMetrics() instead
   */
  protected async flushMetrics(): Promise<void> {
    return this.unifiedMonitor.flushMetrics();
  }

  /**
   * Start a timer
   * @deprecated Use unifiedPerformanceMonitor.startTimer() instead
   */
  private startTimerImpl(name: string): void {
    this.unifiedMonitor.startTimer(name);
  }

  /**
   * End a timer and record the duration
   * @deprecated Use unifiedPerformanceMonitor.endTimer() instead
   */
  private endTimerImpl(name: string): void {
    this.unifiedMonitor.endTimer(name);
  }

  /**
   * Record a metric
   * @deprecated Use unifiedPerformanceMonitor.recordMetric() instead
   */
  private recordMetricImpl(metric: any): void {
    this.unifiedMonitor.recordMetric(metric);
  }
}

/**
 * Create a frontend performance monitoring instance
 * @deprecated Use UnifiedPerformanceMonitor.getInstance() instead
 */
export function createPerformanceMonitoring(
  options: Partial<PerformanceMonitoringOptions> = {},
): FrontendPerformanceMonitoring {
  return FrontendPerformanceMonitoring.getInstance(options);
}