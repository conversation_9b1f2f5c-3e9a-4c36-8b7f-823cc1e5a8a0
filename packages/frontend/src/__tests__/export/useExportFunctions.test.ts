import { renderHook } from "@testing-library/react";
import { useExportFunctions } from "@/pages/export/hooks/useExportFunctions";
import { vi, describe, it, expect, beforeEach } from "vitest";

// Mock dependencies
vi.mock("sonner", () => ({
  toast: {
    info: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
}));

vi.mock("@/contexts/LanguageContext", () => ({
  useLanguage: () => ({
    t: (key: string) => key,
    language: "en",
  }),
}));

// Mock all the specialized export hooks
vi.mock("@/pages/export/hooks/useExcelExport", () => ({
  useExcelExport: () => ({
    exportToExcel: vi.fn(),
    exportMetricsToExcel: vi.fn(),
  }),
}));

vi.mock("@/pages/export/hooks/useJsonExport", () => ({
  useJsonExport: () => ({
    exportToJson: vi.fn(),
  }),
}));

vi.mock("@/pages/export/hooks/useCsvExport", () => ({
  useCsvExport: () => ({
    exportToCsv: vi.fn(),
    exportMetricsToCsv: vi.fn(),
  }),
}));

vi.mock("@/pages/export/hooks/useZipExport", () => ({
  useZipExport: () => ({
    exportToZip: vi.fn(),
  }),
}));

describe("useExportFunctions", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should initialize with default state", () => {
    const { result } = renderHook(() => useExportFunctions());

    expect(result.current.isExporting).toBe(false);
    expect(result.current.exportProgress).toBe(0);
    expect(typeof result.current.exportData).toBe("function");
    expect(typeof result.current.exportSelected).toBe("function");
    expect(typeof result.current.exportMetrics).toBe("function");
    expect(typeof result.current.batchExport).toBe("function");
  });

  it("should provide all export functions", () => {
    const { result } = renderHook(() => useExportFunctions());

    const returnedKeys = Object.keys(result.current);
    expect(returnedKeys).toContain("exportData");
    expect(returnedKeys).toContain("exportSelected");
    expect(returnedKeys).toContain("exportMetrics");
    expect(returnedKeys).toContain("batchExport");
    expect(returnedKeys).toContain("isExporting");
    expect(returnedKeys).toContain("exportProgress");
  });
});