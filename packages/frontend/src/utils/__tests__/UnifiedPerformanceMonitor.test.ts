import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { UnifiedPerformanceMonitor } from '../performance/UnifiedPerformanceMonitor';

// Mock dependencies
vi.mock('@/utils/logging/unifiedLogger', () => ({
  getLogger: vi.fn(() => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  })),
}));

vi.mock('@/services/api/client', () => ({
  default: {
    post: vi.fn(),
  },
}));

describe('UnifiedPerformanceMonitor', () => {
  let monitor: UnifiedPerformanceMonitor;

  beforeEach(() => {
    monitor = new UnifiedPerformanceMonitor({
      enabled: true,
      endpoint: '/test-metrics',
      flushInterval: 0, // Disable auto-flush for tests
    });
  });

  afterEach(() => {
    monitor.destroy();
    vi.clearAllMocks();
  });

  describe('Timer functionality', () => {
    it('should start and end timers correctly', () => {
      const timerName = 'test-timer';
      
      monitor.startTimer(timerName);
      
      // Simulate some work
      const start = performance.now();
      while (performance.now() - start < 10) {
        // busy wait for 10ms
      }
      
      const duration = monitor.endTimer(timerName);
      
      expect(duration).toBeGreaterThan(0);
      expect(duration).toBeLessThan(100); // Should be reasonable
    });

    it('should return null for non-existent timers', () => {
      const duration = monitor.endTimer('non-existent-timer');
      expect(duration).toBeNull();
    });

    it('should track historical metrics', () => {
      const timerName = 'test-timer';
      
      // Run multiple times
      for (let i = 0; i < 3; i++) {
        monitor.startTimer(timerName);
        monitor.endTimer(timerName);
      }
      
      const stats = monitor.getStats(timerName);
      expect(stats).toBeDefined();
      expect(stats?.count).toBe(3);
      expect(stats?.averageTime).toBeGreaterThan(0);
    });
  });

  describe('Operation tracking', () => {
    it('should start and end operations correctly', () => {
      const operationName = 'test-operation';
      
      monitor.startOperation(operationName);
      const duration = monitor.endOperation(operationName);
      
      expect(duration).toBeGreaterThan(0);
    });

    it('should track operations with function wrapper', () => {
      const operationName = 'test-function';
      const testFn = vi.fn(() => 'test-result');
      
      const trackedFn = monitor.trackOperation(operationName, testFn);
      const result = trackedFn();
      
      expect(testFn).toHaveBeenCalled();
      expect(result).toBe('test-result');
      
      const stats = monitor.getStats(operationName);
      expect(stats).toBeDefined();
      expect(stats?.count).toBe(1);
    });

    it('should handle async operations', async () => {
      const operationName = 'async-operation';
      const asyncFn = vi.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async-result';
      });
      
      const trackedFn = monitor.trackOperation(operationName, asyncFn);
      const result = await trackedFn();
      
      expect(asyncFn).toHaveBeenCalled();
      expect(result).toBe('async-result');
      
      const stats = monitor.getStats(operationName);
      expect(stats).toBeDefined();
      expect(stats?.count).toBe(1);
    });
  });

  describe('Metrics recording', () => {
    it('should record custom metrics', () => {
      const metric = {
        type: 'pageLoad' as const,
        timestamp: Date.now(),
        value: 1000,
        route: '/test',
      };
      
      monitor.recordMetric(metric);
      
      // Metric should be recorded internally
      expect(() => monitor.recordMetric(metric)).not.toThrow();
    });

    it('should record component render metrics', () => {
      monitor.recordComponentRender('TestComponent', 50);
      
      // Should not throw and should be recorded
      expect(() => monitor.recordComponentRender('TestComponent', 50)).not.toThrow();
    });

    it('should record API request metrics', () => {
      monitor.recordApiRequest('/api/test', 'GET', 200, 200);
      
      // Should not throw and should be recorded
      expect(() => monitor.recordApiRequest('/api/test', 'GET', 200, 200)).not.toThrow();
    });

    it('should record user interaction metrics', () => {
      monitor.recordUserInteraction('click', 'button', 100);
      
      // Should not throw and should be recorded
      expect(() => monitor.recordUserInteraction('click', 'button', 100)).not.toThrow();
    });
  });

  describe('Statistics and reporting', () => {
    it('should provide stats for all operations', () => {
      monitor.startTimer('timer1');
      monitor.endTimer('timer1');
      
      monitor.startOperation('op1');
      monitor.endOperation('op1');
      
      const allStats = monitor.getStats();
      expect(typeof allStats).toBe('object');
      expect(Object.keys(allStats).length).toBeGreaterThan(0);
    });

    it('should generate performance report', () => {
      monitor.startTimer('test-timer');
      monitor.endTimer('test-timer');
      
      const report = monitor.getReport();
      expect(report).toContain('Unified Performance Report');
      expect(report).toContain('test-timer');
    });

    it('should reset metrics correctly', () => {
      monitor.startTimer('timer1');
      monitor.endTimer('timer1');
      
      monitor.reset();
      
      const stats = monitor.getStats('timer1');
      expect(stats).toBeNull();
    });

    it('should reset specific metrics', () => {
      monitor.startTimer('timer1');
      monitor.endTimer('timer1');
      
      monitor.startTimer('timer2');
      monitor.endTimer('timer2');
      
      monitor.reset('timer1');
      
      const stats1 = monitor.getStats('timer1');
      const stats2 = monitor.getStats('timer2');
      
      expect(stats1).toBeNull();
      expect(stats2).toBeDefined();
    });
  });

  describe('Singleton behavior', () => {
    it('should return the same instance', () => {
      const instance1 = UnifiedPerformanceMonitor.getInstance();
      const instance2 = UnifiedPerformanceMonitor.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('should update options on existing instance', () => {
      const instance1 = UnifiedPerformanceMonitor.getInstance({ enabled: true });
      const instance2 = UnifiedPerformanceMonitor.getInstance({ enabled: false });
      
      expect(instance1).toBe(instance2);
      // Options should be updated (though we can't directly test this without exposing options)
    });
  });
});