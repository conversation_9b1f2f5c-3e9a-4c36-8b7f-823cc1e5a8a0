import { getLogger } from '@/utils/logging/unifiedLogger';
import apiClient from "@/services/api/client";

// Get logger for performance monitoring
const logger = getLogger('unified-performance');

// Re-export types from the monitoring module
export type MetricType =
  | "pageLoad"
  | "componentRender"
  | "apiRequest"
  | "resourceLoad"
  | "userInteraction"
  | "memoryUsage";

export enum MetricTypeEnum {
  PAGE_LOAD = "pageLoad",
  COMPONENT_RENDER = "componentRender",
  API_REQUEST = "apiRequest",
  RESOURCE_LOAD = "resourceLoad",
  USER_INTERACTION = "userInteraction",
  MEMORY_USAGE = "memoryUsage",
}

export interface PerformanceMetric {
  type: MetricType;
  timestamp: number;
  value: number;
  [key: string]: any;
}

export interface PerformanceMonitoringOptions {
  endpoint?: string;
  enabled?: boolean;
  globalLabels?: Record<string, string>;
  flushInterval?: number;
}

export interface TimerStats {
  count: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
}

/**
 * Unified Performance Monitor class that consolidates all performance monitoring functionality
 * Combines the best features from PerformanceMonitor and FrontendPerformanceMonitoring
 */
export class UnifiedPerformanceMonitor {
  private static instance: UnifiedPerformanceMonitor | null = null;
  
  private options: PerformanceMonitoringOptions;
  private timers: Map<string, number> = new Map();
  private metrics: PerformanceMetric[] = [];
  private historicalMetrics: Map<string, number[]> = new Map();
  private activeOperations: Map<string, number> = new Map();
  private performanceObserver: PerformanceObserver | null = null;
  private memoryInterval: NodeJS.Timeout | null = null;
  private flushInterval: NodeJS.Timeout | null = null;

  /**
   * Get singleton instance
   */
  public static getInstance(
    options?: Partial<PerformanceMonitoringOptions>
  ): UnifiedPerformanceMonitor {
    if (!UnifiedPerformanceMonitor.instance) {
      UnifiedPerformanceMonitor.instance = new UnifiedPerformanceMonitor(options);
    } else if (options) {
      UnifiedPerformanceMonitor.instance.updateOptions(options);
    }
    return UnifiedPerformanceMonitor.instance;
  }

  constructor(options: Partial<PerformanceMonitoringOptions> = {}) {
    this.options = {
      enabled: import.meta.env.PROD || import.meta.env.VITE_ENABLE_MONITORING === "true",
      endpoint: `${import.meta.env.VITE_API_BASE_URL || ""}/api/metrics`,
      flushInterval: 30000, // 30 seconds
      ...options,
      globalLabels: {
        app: "frontend",
        environment: import.meta.env.MODE || "development",
        ...options.globalLabels,
      },
    };

    // Set up performance observers and intervals if in browser environment
    if (typeof window !== "undefined" && this.options.enabled) {
      this.setupPerformanceObservers();
      this.setupFlushInterval();
    }
  }

  /**
   * Update monitor options
   */
  public updateOptions(options: Partial<PerformanceMonitoringOptions>): void {
    this.options = {
      ...this.options,
      ...options,
      globalLabels: {
        ...this.options.globalLabels,
        ...options.globalLabels,
      },
    };
  }

  /**
   * Start a timer with the given name
   */
  public startTimer(name: string): void {
    this.timers.set(name, performance.now());
    logger.debug(`Timer started: ${name}`);
  }

  /**
   * End a timer and record the duration
   */
  public endTimer(name: string): number | null {
    const startTime = this.timers.get(name);
    if (startTime === undefined) {
      logger.warn(`Timer not found: ${name}`);
      return null;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);

    // Record to historical metrics
    if (!this.historicalMetrics.has(name)) {
      this.historicalMetrics.set(name, []);
    }
    this.historicalMetrics.get(name)!.push(duration);

    // Record as metric
    this.recordMetric({
      type: MetricTypeEnum.COMPONENT_RENDER,
      timestamp: Date.now(),
      value: duration,
      name,
      duration,
    });

    logger.debug(`Timer ended: ${name}`, { duration: duration.toFixed(2) });
    return duration;
  }

  /**
   * Start an operation timer
   */
  public startOperation(operationName: string): void {
    this.activeOperations.set(operationName, performance.now());
    logger.debug(`Operation started: ${operationName}`);
  }

  /**
   * End an operation timer and record the duration
   */
  public endOperation(operationName: string): number | null {
    const startTime = this.activeOperations.get(operationName);
    if (startTime === undefined) {
      logger.warn(`Operation not found: ${operationName}`);
      return null;
    }

    const duration = performance.now() - startTime;
    this.activeOperations.delete(operationName);

    // Record to historical metrics
    if (!this.historicalMetrics.has(operationName)) {
      this.historicalMetrics.set(operationName, []);
    }
    this.historicalMetrics.get(operationName)!.push(duration);

    // Record as metric
    this.recordMetric({
      type: MetricTypeEnum.COMPONENT_RENDER,
      timestamp: Date.now(),
      value: duration,
      name: operationName,
      duration,
    });

    logger.debug(`Operation ended: ${operationName}`, { duration: duration.toFixed(2) });
    return duration;
  }

  /**
   * Track operation execution time for sync/async functions
   */
  public trackOperation<T extends (...args: any[]) => any>(
    operationName: string,
    fn: T
  ): T {
    return ((...args: Parameters<T>) => {
      this.startOperation(operationName);
      
      try {
        const result = fn(...args);
        
        // Handle async functions
        if (result && typeof result.then === 'function') {
          return result
            .then((value: any) => {
              this.endOperation(operationName);
              return value;
            })
            .catch((error: any) => {
              this.endOperation(operationName);
              throw error;
            });
        } else {
          // Handle sync functions
          this.endOperation(operationName);
          return result;
        }
      } catch (error) {
        this.endOperation(operationName);
        throw error;
      }
    }) as T;
  }

  /**
   * Record a performance metric
   */
  public recordMetric(metric: PerformanceMetric): void {
    if (!this.options.enabled) {
      return;
    }

    const enhancedMetric: PerformanceMetric = {
      ...metric,
      ...this.options.globalLabels,
      timestamp: metric.timestamp || Date.now(),
    };

    this.metrics.push(enhancedMetric);
    logger.debug(`Metric recorded: ${metric.type}`, enhancedMetric);
  }

  /**
   * Record component render time
   */
  public recordComponentRender(component: string, renderTime: number): void {
    this.recordMetric({
      type: MetricTypeEnum.COMPONENT_RENDER,
      timestamp: Date.now(),
      value: renderTime,
      component,
      renderTime,
    });
  }

  /**
   * Record API request metric
   */
  public recordApiRequest(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    error?: string
  ): void {
    this.recordMetric({
      type: MetricTypeEnum.API_REQUEST,
      timestamp: Date.now(),
      value: duration,
      endpoint,
      method,
      duration,
      status,
      error,
    });
  }

  /**
   * Record user interaction metric
   */
  public recordUserInteraction(
    action: string,
    target: string,
    duration: number
  ): void {
    this.recordMetric({
      type: MetricTypeEnum.USER_INTERACTION,
      timestamp: Date.now(),
      value: duration,
      action,
      target,
      duration,
    });
  }

  /**
   * Get statistics for a specific timer/operation
   */
  public getStats(operationName?: string): Record<string, TimerStats> | TimerStats | null {
    if (operationName) {
      const values = this.historicalMetrics.get(operationName);
      if (!values || values.length === 0) {
        return null;
      }

      return {
        count: values.length,
        totalTime: values.reduce((sum, val) => sum + val, 0),
        averageTime: values.reduce((sum, val) => sum + val, 0) / values.length,
        minTime: Math.min(...values),
        maxTime: Math.max(...values),
      };
    }

    // Return stats for all operations
    const stats: Record<string, TimerStats> = {};
    for (const [name, values] of this.historicalMetrics.entries()) {
      if (values.length > 0) {
        const totalTime = values.reduce((sum, val) => sum + val, 0);
        stats[name] = {
          count: values.length,
          totalTime,
          averageTime: totalTime / values.length,
          minTime: Math.min(...values),
          maxTime: Math.max(...values),
        };
      }
    }

    return stats;
  }

  /**
   * Get a performance report
   */
  public getReport(): string {
    const stats = this.getStats() as Record<string, TimerStats>;
    const lines = ['Unified Performance Report:', '============================'];

    for (const [operation, data] of Object.entries(stats)) {
      lines.push(
        `${operation}: ${data.count} calls, avg: ${data.averageTime.toFixed(2)}ms, total: ${data.totalTime.toFixed(2)}ms`
      );
    }

    return lines.join('\n');
  }

  /**
   * Reset metrics for a specific operation or all operations
   */
  public reset(operationName?: string): void {
    if (operationName) {
      this.historicalMetrics.delete(operationName);
      this.timers.delete(operationName);
      this.activeOperations.delete(operationName);
    } else {
      this.historicalMetrics.clear();
      this.timers.clear();
      this.activeOperations.clear();
    }

    logger.debug(`Metrics reset${operationName ? ` for ${operationName}` : ''}`);
  }

  /**
   * Get all historical metrics
   */
  public getAllMetrics(): Map<string, number[]> {
    return new Map(this.historicalMetrics);
  }

  /**
   * Set up performance observers for automatic metrics collection
   */
  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) {
      logger.debug('PerformanceObserver not available');
      return;
    }

    try {
      // Combined observer for multiple entry types
      this.performanceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          switch (entry.entryType) {
            case 'resource':
              this.recordResourceLoad(entry as PerformanceResourceTiming);
              break;
            case 'navigation':
              this.recordPageLoad(entry as PerformanceNavigationTiming);
              break;
            case 'paint':
              this.recordPaintMetric(entry);
              break;
            case 'largest-contentful-paint':
              this.recordLCP(entry);
              break;
            case 'first-input':
              this.recordFID(entry);
              break;
          }
        });
      });

      // Observe all relevant entry types
      this.performanceObserver.observe({ 
        entryTypes: ['resource', 'navigation', 'paint'] 
      });

      // Try to observe newer metrics with separate observers for better compatibility
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            this.recordLCP(lastEntry);
          }
        });
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (e) {
        logger.debug('LCP observer not supported');
      }

      try {
        const fidObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordFID(entry);
          });
        });
        fidObserver.observe({ type: 'first-input', buffered: true });
      } catch (e) {
        logger.debug('FID observer not supported');
      }

    } catch (error) {
      logger.error('Performance observer setup failed', error);
    }

    // Set up memory monitoring if available
    if (performance && (performance as any).memory) {
      this.memoryInterval = setInterval(() => {
        this.recordMemoryUsage();
      }, 30000); // Every 30 seconds
    }
  }

  /**
   * Set up automatic metrics flushing
   */
  private setupFlushInterval(): void {
    if (this.options.flushInterval && this.options.flushInterval > 0) {
      this.flushInterval = setInterval(() => {
        this.flushMetrics();
      }, this.options.flushInterval);
    }
  }

  /**
   * Record resource load metric
   */
  private recordResourceLoad(entry: PerformanceResourceTiming): void {
    // Skip monitoring requests to avoid circular reporting
    if (entry.name.includes(this.options.endpoint || '/api/metrics')) {
      return;
    }

    this.recordMetric({
      type: MetricTypeEnum.RESOURCE_LOAD,
      timestamp: Date.now(),
      value: entry.duration,
      resourceUrl: entry.name,
      resourceType: entry.initiatorType,
      loadTime: entry.duration,
      size: entry.transferSize,
    });
  }

  /**
   * Record page load metric
   */
  private recordPageLoad(entry: PerformanceNavigationTiming): void {
    this.recordMetric({
      type: MetricTypeEnum.PAGE_LOAD,
      timestamp: Date.now(),
      value: entry.duration,
      route: window.location.pathname,
      loadTime: entry.duration,
      domContentLoaded: entry.domContentLoadedEventEnd - entry.startTime,
    });
  }

  /**
   * Record paint metric (FP, FCP)
   */
  private recordPaintMetric(entry: PerformanceEntry): void {
    this.recordMetric({
      type: MetricTypeEnum.PAGE_LOAD,
      timestamp: Date.now(),
      value: entry.startTime,
      paintType: entry.name,
      startTime: entry.startTime,
    });
  }

  /**
   * Record Largest Contentful Paint
   */
  private recordLCP(entry: PerformanceEntry): void {
    this.recordMetric({
      type: MetricTypeEnum.PAGE_LOAD,
      timestamp: Date.now(),
      value: entry.startTime,
      route: window.location.pathname,
      largestContentfulPaint: entry.startTime,
    });
  }

  /**
   * Record First Input Delay
   */
  private recordFID(entry: PerformanceEntry): void {
    this.recordMetric({
      type: MetricTypeEnum.USER_INTERACTION,
      timestamp: Date.now(),
      value: entry.duration || 0,
      action: 'first-input',
      target: 'document',
      duration: entry.duration || 0,
    });
  }

  /**
   * Record memory usage metric
   */
  private recordMemoryUsage(): void {
    try {
      const memory = (performance as any).memory;
      if (memory) {
        this.recordMetric({
          type: MetricTypeEnum.MEMORY_USAGE,
          timestamp: Date.now(),
          value: memory.usedJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          totalJSHeapSize: memory.totalJSHeapSize,
          usedJSHeapSize: memory.usedJSHeapSize,
        });
      }
    } catch (error) {
      logger.debug('Memory usage recording failed', error);
    }
  }

  /**
   * Flush metrics to the server
   */
  public async flushMetrics(): Promise<void> {
    if (!this.options.enabled || this.metrics.length === 0) {
      return;
    }

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      await apiClient.post(
        this.options.endpoint || '/api/metrics',
        { metrics: metricsToSend },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      logger.debug(`Flushed ${metricsToSend.length} metrics to server`);
    } catch (error) {
      // Put metrics back in the queue to try again later
      this.metrics = [...metricsToSend, ...this.metrics];
      logger.debug('Failed to flush metrics to server', error);
    }
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
      this.memoryInterval = null;
    }

    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    this.reset();
    logger.debug('UnifiedPerformanceMonitor destroyed');
  }
}

// Export a singleton instance
export const unifiedPerformanceMonitor = UnifiedPerformanceMonitor.getInstance();

// Note: Types are already exported above where they're defined