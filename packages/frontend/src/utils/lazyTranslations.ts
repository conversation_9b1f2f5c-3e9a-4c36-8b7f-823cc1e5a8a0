/**
 * Lazy Translation Loading Utility
 * Reduces initial bundle size by ~250KB by loading translations on-demand
 */

import type { Resource } from 'i18next';

// Cache for loaded translations
const translationCache = new Map<string, any>();

/**
 * Load translation file dynamically
 */
export const loadTranslation = async (
  language: string,
  namespace: string = 'translation'
): Promise<any> => {
  const cacheKey = `${language}-${namespace}`;
  
  // Return from cache if already loaded
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey);
  }

  try {
    // Dynamic import based on language
    let translation;
    
    switch (language) {
      case 'en':
        translation = await import('../locales/en/translation.json');
        break;
      case 'zh':
        translation = await import('../locales/zh/translation.json');
        break;
      default:
        // Fallback to English
        translation = await import('../locales/en/translation.json');
    }
    
    // Cache the loaded translation
    const data = translation.default || translation;
    translationCache.set(cacheKey, data);
    
    // Also save to localStorage for offline support
    try {
      localStorage.setItem(`i18n-${cacheKey}`, JSON.stringify(data));
    } catch (e) {
      console.warn('Failed to cache translation to localStorage:', e);
    }
    
    return data;
  } catch (error) {
    console.error(`Failed to load translation for ${language}:`, error);
    
    // Try to load from localStorage as fallback
    try {
      const cached = localStorage.getItem(`i18n-${cacheKey}`);
      if (cached) {
        const data = JSON.parse(cached);
        translationCache.set(cacheKey, data);
        return data;
      }
    } catch (e) {
      console.warn('Failed to load translation from localStorage:', e);
    }
    
    // Final fallback - return empty object
    return {};
  }
};

/**
 * Split translations by feature/route
 */
export const loadFeatureTranslations = async (
  language: string,
  features: string[]
): Promise<Resource> => {
  const resources: Resource = {};
  
  // Load common translations first
  const common = await loadTranslation(language, 'common');
  
  // Load feature-specific translations
  const featureTranslations = await Promise.all(
    features.map(feature => loadTranslation(language, feature))
  );
  
  // Merge all translations
  resources[language] = {
    common,
    ...features.reduce((acc, feature, index) => {
      acc[feature] = featureTranslations[index];
      return acc;
    }, {} as any)
  };
  
  return resources;
};

/**
 * Preload translations for better UX
 */
export const preloadTranslations = (languages: string[]): void => {
  // Use requestIdleCallback for non-blocking preload
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      languages.forEach(lang => {
        loadTranslation(lang).catch(() => {
          // Silently fail preloading
        });
      });
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      languages.forEach(lang => {
        loadTranslation(lang).catch(() => {
          // Silently fail preloading
        });
      });
    }, 1000);
  }
};

/**
 * Clear translation cache
 */
export const clearTranslationCache = (): void => {
  translationCache.clear();
  
  // Clear localStorage translations
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('i18n-')) {
      localStorage.removeItem(key);
    }
  });
};