/**
 * Lazy Import Utilities for Heavy Libraries
 * Reduces initial bundle by ~400KB by loading libraries on-demand
 */

import { lazy } from 'react';

// Type definitions for lazy loaded modules
export interface ExcelModule {
  utils: any;
  writeFile: (workbook: any, filename: string) => void;
  writeFileXLSX: (workbook: any, filename: string) => void;
}

export interface PDFModule {
  jsPDF: any;
}

export interface JimpModule {
  read: (data: any) => Promise<any>;
  create: (width: number, height: number) => any;
}

// Cache for loaded modules
const moduleCache = new Map<string, any>();

/**
 * Load XLSX library for Excel export
 */
export const loadXLSX = async (): Promise<ExcelModule> => {
  if (moduleCache.has('xlsx')) {
    return moduleCache.get('xlsx');
  }
  
  try {
    const module = await import('xlsx');
    moduleCache.set('xlsx', module);
    return module as unknown as ExcelModule;
  } catch (error) {
    console.error('Failed to load XLSX library:', error);
    throw new Error('Excel export functionality is currently unavailable');
  }
};

/**
 * Load jsPDF for PDF generation
 */
export const loadJsPDF = async (): Promise<PDFModule> => {
  if (moduleCache.has('jspdf')) {
    return moduleCache.get('jspdf');
  }
  
  try {
    const module = await import('jspdf');
    moduleCache.set('jspdf', module);
    return module as PDFModule;
  } catch (error) {
    console.error('Failed to load jsPDF library:', error);
    throw new Error('PDF export functionality is currently unavailable');
  }
};

/**
 * Load Jimp for image processing
 */
export const loadJimp = async (): Promise<JimpModule> => {
  if (moduleCache.has('jimp')) {
    return moduleCache.get('jimp');
  }
  
  try {
    const module = await import('jimp');
    moduleCache.set('jimp', module.default || module);
    return module.default || module;
  } catch (error) {
    console.error('Failed to load Jimp library:', error);
    throw new Error('Image processing functionality is currently unavailable');
  }
};

/**
 * Load JSZip for file compression
 */
export const loadJSZip = async () => {
  if (moduleCache.has('jszip')) {
    return moduleCache.get('jszip');
  }
  
  try {
    const module = await import('jszip');
    moduleCache.set('jszip', module.default || module);
    return module.default || module;
  } catch (error) {
    console.error('Failed to load JSZip library:', error);
    throw new Error('File compression functionality is currently unavailable');
  }
};

/**
 * Load Recharts for data visualization
 */
export const loadRecharts = async () => {
  if (moduleCache.has('recharts')) {
    return moduleCache.get('recharts');
  }
  
  try {
    const module = await import('recharts');
    moduleCache.set('recharts', module);
    return module;
  } catch (error) {
    console.error('Failed to load Recharts library:', error);
    throw new Error('Charts functionality is currently unavailable');
  }
};

/**
 * Lazy load React components
 */
export const LazyComponents = {
  // Pages
  Dashboard: lazy(() => import('../pages/Dashboard')),
  ProjectDetail: lazy(() => import('../pages/ProjectDetail')),
  Settings: lazy(() => import('../pages/Settings')),
  Profile: lazy(() => import('../pages/Profile')),
  
  // Export features
  ProjectExport: lazy(() => import('../pages/export/ProjectExport')),
  
  // Segmentation features
  SegmentationPage: lazy(() => import('../pages/segmentation/SegmentationPage')),
  
  // Heavy UI components - commented out non-existent components
  // DataTable: lazy(() => import('../components/DataTable')),
  // ChartDashboard: lazy(() => import('../components/ChartDashboard')),
  // ImageEditor: lazy(() => import('../components/ImageEditor')),
};

/**
 * Preload modules for better UX
 */
export const preloadModule = (moduleName: string): void => {
  // Use requestIdleCallback for non-blocking preload
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      switch (moduleName) {
        case 'xlsx':
          loadXLSX().catch(() => {});
          break;
        case 'jspdf':
          loadJsPDF().catch(() => {});
          break;
        case 'jimp':
          loadJimp().catch(() => {});
          break;
        case 'jszip':
          loadJSZip().catch(() => {});
          break;
        case 'recharts':
          loadRecharts().catch(() => {});
          break;
      }
    });
  }
};

/**
 * Clear module cache
 */
export const clearModuleCache = (): void => {
  moduleCache.clear();
};

/**
 * Check if module is loaded
 */
export const isModuleLoaded = (moduleName: string): boolean => {
  return moduleCache.has(moduleName);
};

/**
 * Get module load status
 */
export const getModuleStatus = (): Map<string, boolean> => {
  const status = new Map<string, boolean>();
  ['xlsx', 'jspdf', 'jimp', 'jszip', 'recharts'].forEach(module => {
    status.set(module, moduleCache.has(module));
  });
  return status;
};