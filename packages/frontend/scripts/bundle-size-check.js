#!/usr/bin/env node
/**
 * Bundle Size Monitoring Script
 * Checks bundle sizes against defined budgets and reports violations
 */

const fs = require('fs');
const path = require('path');
const { gzipSync } = require('zlib');
const chalk = require('chalk');

// Bundle size budgets (in KB)
const BUDGETS = {
  // Main bundles
  'index': { max: 50, warn: 40 },
  'vendor-react-core': { max: 80, warn: 70 },
  'vendor-react-dom': { max: 45, warn: 40 },
  'vendor-critical': { max: 30, warn: 25 },
  
  // Feature bundles
  'feature-': { max: 100, warn: 80 }, // Prefix match for all features
  'vendor-': { max: 100, warn: 80 },  // Prefix match for all vendors
  
  // Language bundles
  'lang-': { max: 50, warn: 40 },     // Prefix match for all languages
  'translations': { max: 100, warn: 80 },
  
  // Heavy libraries (should be lazy loaded)
  'vendor-xlsx': { max: 100, warn: 90 },
  'vendor-pdf': { max: 50, warn: 40 },
  'vendor-charts': { max: 80, warn: 70 },
  'vendor-image': { max: 60, warn: 50 },
  
  // Total budget
  '_total': { max: 1000, warn: 800 },
  '_initial': { max: 400, warn: 350 }, // Initial load budget
};

// Files that should be loaded initially
const INITIAL_LOAD_PATTERNS = [
  'index',
  'vendor-react-core',
  'vendor-react-dom',
  'vendor-critical',
  'vendor-ui-base',
  'vendor-utils',
];

/**
 * Get file size in KB
 */
function getFileSizeInKB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size / 1024;
  } catch (error) {
    return 0;
  }
}

/**
 * Get gzipped size in KB
 */
function getGzippedSizeInKB(filePath) {
  try {
    const content = fs.readFileSync(filePath);
    const gzipped = gzipSync(content);
    return gzipped.length / 1024;
  } catch (error) {
    return 0;
  }
}

/**
 * Format size for display
 */
function formatSize(sizeInKB) {
  if (sizeInKB >= 1024) {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
  return `${sizeInKB.toFixed(2)} KB`;
}

/**
 * Get budget for a file
 */
function getBudget(fileName) {
  // Check exact match first
  const baseName = path.basename(fileName, '.js').replace(/-[a-zA-Z0-9]+$/, '');
  if (BUDGETS[baseName]) {
    return BUDGETS[baseName];
  }
  
  // Check prefix matches
  for (const [pattern, budget] of Object.entries(BUDGETS)) {
    if (pattern.endsWith('-') && baseName.startsWith(pattern.slice(0, -1))) {
      return budget;
    }
  }
  
  // Default budget
  return { max: 100, warn: 80 };
}

/**
 * Check if file is part of initial load
 */
function isInitialLoad(fileName) {
  const baseName = path.basename(fileName, '.js').replace(/-[a-zA-Z0-9]+$/, '');
  return INITIAL_LOAD_PATTERNS.some(pattern => baseName.includes(pattern));
}

/**
 * Analyze bundle sizes
 */
function analyzeBundles() {
  const distPath = path.join(__dirname, '../dist');
  const assetsPath = path.join(distPath, 'assets');
  
  if (!fs.existsSync(assetsPath)) {
    console.error(chalk.red('❌ Build directory not found. Please run build first.'));
    process.exit(1);
  }
  
  // Find all JS files
  const jsFiles = fs.readdirSync(assetsPath)
    .filter(file => file.endsWith('.js'))
    .map(file => path.join(assetsPath, file));
  
  // Analyze each file
  const results = [];
  let totalSize = 0;
  let totalGzipped = 0;
  let initialSize = 0;
  let initialGzipped = 0;
  
  for (const file of jsFiles) {
    const fileName = path.basename(file);
    const size = getFileSizeInKB(file);
    const gzipped = getGzippedSizeInKB(file);
    const budget = getBudget(fileName);
    const initial = isInitialLoad(fileName);
    
    totalSize += size;
    totalGzipped += gzipped;
    
    if (initial) {
      initialSize += size;
      initialGzipped += gzipped;
    }
    
    results.push({
      file: fileName,
      size,
      gzipped,
      budget,
      initial,
      status: gzipped > budget.max ? 'error' : gzipped > budget.warn ? 'warn' : 'ok'
    });
  }
  
  // Sort by size (largest first)
  results.sort((a, b) => b.gzipped - a.gzipped);
  
  // Print header
  console.log('\n' + chalk.bold('📊 Bundle Size Report'));
  console.log('=' .repeat(80));
  
  // Print individual files
  console.log(chalk.bold('\n📦 Individual Bundles:'));
  console.log('-'.repeat(80));
  
  const tableData = [];
  for (const result of results) {
    const statusIcon = 
      result.status === 'error' ? '❌' :
      result.status === 'warn' ? '⚠️ ' :
      '✅';
    
    const sizeColor = 
      result.status === 'error' ? chalk.red :
      result.status === 'warn' ? chalk.yellow :
      chalk.green;
    
    tableData.push([
      statusIcon,
      result.file.substring(0, 40).padEnd(40),
      formatSize(result.size).padStart(10),
      sizeColor(formatSize(result.gzipped).padStart(10)),
      `${formatSize(result.budget.warn)} / ${formatSize(result.budget.max)}`.padStart(20),
      result.initial ? '📥' : ''
    ]);
  }
  
  // Print table header
  console.log(
    '   ',
    'File'.padEnd(40),
    'Size'.padStart(10),
    'Gzipped'.padStart(10),
    'Budget (warn/max)'.padStart(20),
    'Init'
  );
  console.log('-'.repeat(80));
  
  // Print table rows
  tableData.forEach(row => {
    console.log(row.join(' '));
  });
  
  // Print totals
  console.log('\n' + chalk.bold('📈 Summary:'));
  console.log('-'.repeat(80));
  
  const totalBudget = BUDGETS._total;
  const initialBudget = BUDGETS._initial;
  
  const totalStatus = 
    totalGzipped > totalBudget.max ? 'error' :
    totalGzipped > totalBudget.warn ? 'warn' :
    'ok';
  
  const initialStatus = 
    initialGzipped > initialBudget.max ? 'error' :
    initialGzipped > initialBudget.warn ? 'warn' :
    'ok';
  
  console.log(`Total Bundle Size:    ${formatSize(totalSize)} (${chalk.bold(formatSize(totalGzipped))} gzipped)`);
  console.log(`Total Budget:         ${formatSize(totalBudget.warn)} / ${formatSize(totalBudget.max)}`);
  console.log(`Status:               ${totalStatus === 'error' ? chalk.red('❌ OVER BUDGET') : totalStatus === 'warn' ? chalk.yellow('⚠️  WARNING') : chalk.green('✅ OK')}`);
  
  console.log();
  
  console.log(`Initial Load Size:    ${formatSize(initialSize)} (${chalk.bold(formatSize(initialGzipped))} gzipped)`);
  console.log(`Initial Load Budget:  ${formatSize(initialBudget.warn)} / ${formatSize(initialBudget.max)}`);
  console.log(`Status:               ${initialStatus === 'error' ? chalk.red('❌ OVER BUDGET') : initialStatus === 'warn' ? chalk.yellow('⚠️  WARNING') : chalk.green('✅ OK')}`);
  
  // Print violations
  const violations = results.filter(r => r.status === 'error');
  const warnings = results.filter(r => r.status === 'warn');
  
  if (violations.length > 0) {
    console.log('\n' + chalk.red.bold('❌ Budget Violations:'));
    violations.forEach(v => {
      const excess = v.gzipped - v.budget.max;
      console.log(chalk.red(`  - ${v.file}: ${formatSize(v.gzipped)} (${formatSize(excess)} over budget)`));
    });
  }
  
  if (warnings.length > 0) {
    console.log('\n' + chalk.yellow.bold('⚠️  Budget Warnings:'));
    warnings.forEach(w => {
      const nearLimit = w.budget.max - w.gzipped;
      console.log(chalk.yellow(`  - ${w.file}: ${formatSize(w.gzipped)} (${formatSize(nearLimit)} from limit)`));
    });
  }
  
  // Print recommendations
  console.log('\n' + chalk.blue.bold('💡 Recommendations:'));
  
  // Check for large vendor bundles
  const largeVendors = results.filter(r => 
    r.file.includes('vendor-') && r.gzipped > 50
  );
  
  if (largeVendors.length > 0) {
    console.log(chalk.blue('  • Consider lazy loading these vendor bundles:'));
    largeVendors.forEach(v => {
      console.log(chalk.blue(`    - ${v.file} (${formatSize(v.gzipped)})`));
    });
  }
  
  // Check for large feature bundles
  const largeFeatures = results.filter(r => 
    r.file.includes('feature-') && r.gzipped > 80
  );
  
  if (largeFeatures.length > 0) {
    console.log(chalk.blue('  • Consider splitting these feature bundles:'));
    largeFeatures.forEach(f => {
      console.log(chalk.blue(`    - ${f.file} (${formatSize(f.gzipped)})`));
    });
  }
  
  // Check initial load
  if (initialGzipped > initialBudget.warn) {
    console.log(chalk.blue('  • Initial load is too large. Consider:'));
    console.log(chalk.blue('    - Implementing route-based code splitting'));
    console.log(chalk.blue('    - Moving non-critical vendors to lazy loading'));
    console.log(chalk.blue('    - Using CDN for React in production'));
  }
  
  console.log('\n' + '='.repeat(80));
  
  // Exit with error if over budget
  if (violations.length > 0 || totalStatus === 'error' || initialStatus === 'error') {
    process.exit(1);
  }
  
  // Exit with warning
  if (warnings.length > 0 || totalStatus === 'warn' || initialStatus === 'warn') {
    process.exit(0);
  }
}

// Run analysis
analyzeBundles();