import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import compression from 'vite-plugin-compression2';

// Simplified Vite config for production build
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isDevelopment = mode === 'development';
  const isProduction = mode === 'production';

  // When running in Docker, use backend service name
  const isDocker = env.VITE_DOCKER_ENV === 'true';
  const apiUrl = env.VITE_API_URL || (isDocker ? 'http://spheroseg-backend:5001' : 'http://localhost:5001');

  return {
    plugins: [
      react({
        jsxRuntime: 'automatic',
        jsxImportSource: 'react',
        babel: {
          plugins: [
            // Ensure production builds use the correct runtime
            [
              '@babel/plugin-transform-react-jsx',
              {
                runtime: 'automatic',
                importSource: 'react',
                development: isDevelopment,
              },
            ],
          ],
        },
      }),
      // Bundle analyzer (only when ANALYZE=true)
      process.env.ANALYZE && visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
      }),
      // Gzip compression for production
      isProduction && compression({
        algorithm: 'gzip',
        ext: '.gz',
      }),
      // Brotli compression for production
      isProduction && compression({
        algorithm: 'brotliCompress',
        ext: '.br',
      }),
    ].filter(Boolean),
    esbuild: {
      // Ensure esbuild also uses the correct JSX factory
      jsx: 'automatic',
      jsxDev: isDevelopment,
    },
    build: {
      outDir: 'dist',
      sourcemap: isDevelopment ? false : 'hidden', // Hidden sourcemaps in production for debugging
      minify: isProduction ? 'esbuild' : false,
      target: 'es2020',
      chunkSizeWarningLimit: 400, // Set to 400KB target
      rollupOptions: {
        output: {
          // Aggressive code splitting
          manualChunks: (id) => {
            // Vendor chunk for core React libs
            if (id.includes('node_modules')) {
              // Split React core into separate chunks
              if (id.includes('react-dom')) {
                return 'vendor-react-dom';
              }
              if (id.includes('react') && !id.includes('react-dom')) {
                return 'vendor-react-core';
              }
              if (id.includes('react-router')) {
                return 'vendor-router';
              }
              if (id.includes('@radix-ui')) {
                return 'vendor-radix';
              }
              if (id.includes('@tanstack/react-query')) {
                return 'vendor-query';
              }
              if (id.includes('framer-motion')) {
                return 'vendor-motion';
              }
              if (id.includes('lucide-react')) {
                return 'vendor-icons';
              }
              if (id.includes('recharts') || id.includes('d3')) {
                return 'vendor-charts';
              }
              // Split export libraries into separate chunks
              if (id.includes('xlsx')) {
                return 'vendor-xlsx';
              }
              if (id.includes('jspdf')) {
                return 'vendor-pdf';
              }
              if (id.includes('jszip') || id.includes('file-saver')) {
                return 'vendor-compression';
              }
              // Image processing libraries
              if (id.includes('jimp') || id.includes('react-image-crop')) {
                return 'vendor-image';
              }
              // Form and validation libraries
              if (id.includes('react-hook-form') || id.includes('zod') || id.includes('yup')) {
                return 'vendor-forms';
              }
              // Date/time libraries
              if (id.includes('date-fns') || id.includes('dayjs') || id.includes('moment')) {
                return 'vendor-datetime';
              }
              // State management
              if (id.includes('zustand') || id.includes('immer') || id.includes('valtio')) {
                return 'vendor-state';
              }
              // HTTP and API libraries
              if (id.includes('axios') || id.includes('socket.io')) {
                return 'vendor-network';
              }
              // Utility libraries
              if (id.includes('lodash') || id.includes('clsx') || id.includes('classnames')) {
                return 'vendor-utils';
              }
              // Other vendor libraries
              return 'vendor-misc';
            }

            // Split large feature chunks
            if (id.includes('/pages/export/')) {
              return 'feature-export';
            }
            if (id.includes('/pages/segmentation/')) {
              return 'feature-segmentation';
            }
            if (id.includes('/pages/project/')) {
              return 'feature-project';
            }
            if (id.includes('/components/project/')) {
              return 'feature-project';
            }
            
            // Translation chunks
            if (id.includes('/translations/')) {
              return 'translations';
            }
            
            // Services and utilities
            if (id.includes('/services/')) {
              return 'services';
            }
            if (id.includes('/utils/')) {
              return 'utils';
            }
            
            return undefined;
          },
        },
      },
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: true,
      allowedHosts: ['all'], // Allow all hosts for Docker networking
      hmr: {
        port: 3001,
        host: '0.0.0.0',
        clientPort: 3001
      },
      watch: {
        usePolling: isDocker,
        interval: 100
      },
      proxy: {
        '/api': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
          ws: true,
          rewrite: (path) => path, // Keep the /api prefix
          configure: (proxy) => {
            // Add logging for debugging
            proxy.on('error', (err, req, res) => {
              console.error('[Proxy Error]', err.message);
              res.writeHead(500, {
                'Content-Type': 'text/plain',
              });
              res.end('Proxy error: ' + err.message);
            });
            
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log(`[Proxy Request] ${req.method} ${req.url} -> ${apiUrl}${req.url}`);
            });
            
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log(`[Proxy Response] ${req.method} ${req.url} - Status: ${proxyRes.statusCode}`);
            });
          }
        },
        '/uploads': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path, // Keep the /uploads prefix
        },
        '/socket.io': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
          ws: true,
          rewrite: (path) => path, // Keep the /socket.io prefix
        },
      },
    },
    define: {
      'process.env.VITE_API_URL': JSON.stringify(apiUrl),
      'process.env.NODE_ENV': JSON.stringify(mode),
      // Ensure React production mode
      'process.env': JSON.stringify({ NODE_ENV: mode }),
      '__DEV__': JSON.stringify(!isProduction),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@spheroseg/shared': path.resolve(__dirname, '../shared/src'),
      },
    },
  };
});