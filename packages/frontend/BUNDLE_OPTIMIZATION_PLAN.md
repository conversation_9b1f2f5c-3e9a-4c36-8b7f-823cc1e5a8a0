# Bundle Size Optimization Plan for SpheroSeg Frontend

## Context Summary

### Current Bundle Analysis
- **Total dist size**: 16MB (uncompressed)
- **Main chunks exceeding 100KB**:
  - translations-BacSreCa.js: 325KB (97KB gzipped) 
  - vendor-misc-CRbzdsIT.js: 317KB (102KB gzipped)
  - vendor-xlsx-5rFX-EW9.js: 283KB (95KB gzipped)
  - vendor-react-core-Cz7-wil_.js: 245KB (73KB gzipped)
  - vendor-react-dom-QM5BtMae.js: 133KB (43KB gzipped)
  - feature-project-Co7eLTXL.js: 118KB (32KB gzipped)

- **Current chunk limit warning**: 400KB (need to reduce further)
- **Target size**: <400KB for main bundle

## Analysis

### Largest Contributors (Priority Order)

1. **Translation Bundle (325KB)** - HIGHEST IMPACT
   - Currently loading ALL translations upfront
   - Contains full translation objects for multiple languages
   - No dynamic loading or splitting

2. **Excel/Export Libraries (283KB)**
   - xlsx library is massive
   - Only used in export features
   - Should be dynamically imported

3. **Vendor Miscellaneous (317KB)**
   - Contains jimp, jspdf, various utilities
   - Many libraries could be loaded on-demand

4. **React Core Libraries (378KB combined)**
   - React + ReactDOM taking significant space
   - Consider CDN for production

5. **Feature Bundles (100-120KB each)**
   - Project, segmentation, export features
   - Could benefit from more aggressive splitting

## Recommended Approach

### Phase 1: Quick Wins (1-2 days) - Expected 40% reduction

1. **Dynamic Translation Loading**
2. **Lazy Load Export Libraries**
3. **Remove Unused Dependencies**
4. **Enable Additional Compression**

### Phase 2: Code Splitting (3-4 days) - Expected 30% reduction

5. **Route-based Code Splitting**
6. **Component-level Lazy Loading**
7. **Feature Module Splitting**

### Phase 3: Advanced Optimization (1 week) - Expected 20% reduction

8. **CDN Strategy**
9. **Tree Shaking Improvements**
10. **Asset Optimization**

## Implementation Plan

### 1. Dynamic Translation Loading (HIGHEST PRIORITY)
**Impact**: -250KB from initial bundle

```typescript
// src/i18n.ts - Modify to load translations dynamically
const loadTranslation = async (lng: string) => {
  const translation = await import(`./locales/${lng}/translation.json`);
  return translation.default;
};

// Use React.lazy for translation components
const TranslationProvider = lazy(() => import('./providers/TranslationProvider'));
```

**Steps**:
- Split translation files by language
- Load only active language on init
- Implement language switching with dynamic import
- Cache loaded translations in localStorage

### 2. Lazy Load Heavy Libraries
**Impact**: -400KB from initial bundle

```typescript
// Lazy load export utilities
const ExcelExporter = lazy(() => import('xlsx'));
const PDFExporter = lazy(() => import('jspdf'));
const ImageProcessor = lazy(() => import('jimp'));

// Dynamic import on use
const exportToExcel = async (data) => {
  const XLSX = await import('xlsx');
  // Use XLSX here
};
```

**Libraries to lazy load**:
- xlsx (283KB)
- jspdf (part of vendor-misc)
- jimp (image processing)
- jszip (compression)
- recharts (only for dashboard)

### 3. Route-based Code Splitting
**Impact**: -200KB from initial bundle

```typescript
// src/App.tsx - Convert all routes to lazy loading
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ProjectDetail = lazy(() => import('./pages/ProjectDetail'));
const Segmentation = lazy(() => import('./pages/segmentation/SegmentationPage'));
const Settings = lazy(() => import('./pages/Settings'));
const Export = lazy(() => import('./pages/export/ExportPage'));

// Wrap with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/dashboard" element={<Dashboard />} />
  </Routes>
</Suspense>
```

### 4. Vendor Bundle Optimization
**Impact**: -150KB from initial bundle

```typescript
// vite.config.ts - Improve manualChunks
manualChunks: {
  // Core vendor (keep in initial)
  'vendor-react': ['react', 'react-dom'],
  'vendor-router': ['react-router-dom'],
  
  // Lazy loaded vendors
  'vendor-export': ['xlsx', 'jspdf', 'file-saver'],
  'vendor-image': ['jimp', 'react-image-crop'],
  'vendor-charts': ['recharts', 'd3'],
  'vendor-ui-heavy': ['@radix-ui/*'], // Split heavy UI components
}
```

### 5. Translation File Optimization
**Impact**: -150KB per language

```typescript
// Split translations by feature
const translations = {
  common: () => import('./translations/common.json'),
  dashboard: () => import('./translations/dashboard.json'),
  segmentation: () => import('./translations/segmentation.json'),
  export: () => import('./translations/export.json'),
};

// Load based on route
const loadRouteTranslations = async (route: string) => {
  const common = await translations.common();
  const feature = await translations[route]?.();
  return { ...common, ...feature };
};
```

### 6. Remove Unused Dependencies
**Impact**: -50KB

Check and remove:
- regenerator-runtime (if not needed)
- Multiple date libraries (consolidate to date-fns only)
- Duplicate type packages in dependencies (should be devDependencies)

### 7. CDN Strategy for Production
**Impact**: -380KB from bundle

```html
<!-- index.html for production -->
<script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
<script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

<!-- vite.config.ts -->
rollupOptions: {
  external: isProduction ? ['react', 'react-dom'] : [],
  output: {
    globals: {
      react: 'React',
      'react-dom': 'ReactDOM'
    }
  }
}
```

### 8. Asset Optimization
**Impact**: -30KB

- Convert images to WebP format
- Implement responsive image loading
- Use SVG sprites for icons
- Lazy load images below the fold

### 9. Build Configuration Enhancements

```typescript
// vite.config.ts additions
build: {
  target: 'es2020',
  minify: 'terser', // Better minification
  terserOptions: {
    compress: {
      drop_console: true,
      drop_debugger: true,
      pure_funcs: ['console.log'],
    },
  },
  reportCompressedSize: true,
  cssCodeSplit: true,
  assetsInlineLimit: 4096, // Inline small assets
}

// Add preload/prefetch
plugins: [
  {
    name: 'preload-important',
    transformIndexHtml(html) {
      return html.replace(
        '</head>',
        `<link rel="preload" href="/vendor-react.js" as="script">
         <link rel="prefetch" href="/vendor-ui.js" as="script">
         </head>`
      );
    }
  }
]
```

### 10. Tree Shaking Improvements

```json
// package.json
{
  "sideEffects": false,
  "module": "./dist/index.esm.js"
}
```

```typescript
// Use specific imports
import debounce from 'lodash/debounce'; // NOT import _ from 'lodash'
import { format } from 'date-fns'; // NOT import * as dateFns
```

## Risks & Mitigations

### Risk: Lazy loading increases complexity
**Mitigation**: 
- Implement comprehensive error boundaries
- Add retry logic for failed imports
- Provide offline fallbacks

### Risk: Performance regression from splitting
**Mitigation**:
- Implement resource hints (prefetch/preload)
- Use service workers for caching
- Monitor Core Web Vitals

### Risk: Browser compatibility issues
**Mitigation**:
- Keep legacy build for older browsers
- Implement progressive enhancement
- Test on target browser matrix

## Success Criteria

### Bundle Size Targets
- ✅ Initial JS bundle: <400KB (gzipped: <120KB)
- ✅ Largest chunk: <100KB
- ✅ Total cached size: <1MB
- ✅ Time to Interactive: <3s on 3G

### Performance Metrics
- ✅ Lighthouse Performance Score: >90
- ✅ First Contentful Paint: <1.5s
- ✅ Largest Contentful Paint: <2.5s
- ✅ Total Blocking Time: <200ms

## Additional Considerations

### Immediate Actions (Do Today)
1. Audit package.json for unused dependencies
2. Move type packages to devDependencies
3. Enable build compression in CI/CD
4. Set up bundle size monitoring

### Future Improvements
1. Implement Module Federation for micro-frontends
2. Consider Preact for smaller React alternative
3. Investigate WebAssembly for heavy computations
4. Implement progressive web app features

### Monitoring Strategy
1. **Automated Size Checks**: Add to CI/CD pipeline
   ```yaml
   - name: Check bundle size
     run: |
       npm run build
       npx bundlesize --max-size 400KB dist/assets/*.js
   ```

2. **Performance Budget**:
   ```json
   {
     "bundlesize": [
       {
         "path": "./dist/assets/index-*.js",
         "maxSize": "50 kB"
       },
       {
         "path": "./dist/assets/vendor-*.js",
         "maxSize": "100 kB"
       }
     ]
   }
   ```

3. **Real User Monitoring**: Implement performance tracking
   ```typescript
   // Track bundle load times
   performance.mark('bundle-start');
   import('./heavy-module').then(() => {
     performance.mark('bundle-end');
     performance.measure('bundle-load', 'bundle-start', 'bundle-end');
   });
   ```

## Expected Results

### After Phase 1 (Quick Wins)
- Bundle size: ~600KB (-40%)
- Gzipped: ~180KB
- Initial load time: -1.5s

### After Phase 2 (Code Splitting)
- Bundle size: ~400KB (-60%)
- Gzipped: ~120KB
- Initial load time: -2.5s

### After Phase 3 (Full Optimization)
- Bundle size: <300KB (-70%)
- Gzipped: <90KB
- Initial load time: -3s
- Lighthouse score: 95+

## Next Steps

1. **Priority 1**: Implement dynamic translation loading
2. **Priority 2**: Lazy load xlsx and export libraries
3. **Priority 3**: Set up route-based code splitting
4. **Priority 4**: Configure CDN for React in production
5. **Priority 5**: Add bundle size checks to CI/CD

This plan provides a systematic approach to reducing bundle size from >1MB to <400KB target, with measurable improvements at each phase.