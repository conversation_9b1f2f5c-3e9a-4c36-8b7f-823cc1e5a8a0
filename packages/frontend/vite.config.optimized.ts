import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import compression from 'vite-plugin-compression2';
import legacy from '@vitejs/plugin-legacy';

// Optimized Vite configuration for <400KB bundle target
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isDevelopment = mode === 'development';
  const isProduction = mode === 'production';
  const isAnalyze = process.env.ANALYZE === 'true';

  return {
    plugins: [
      // React plugin with optimizations
      react({
        jsxRuntime: 'automatic',
        jsxImportSource: 'react',
        // No extra babel plugins needed - esbuild handles optimizations
      }),
      
      // Bundle analyzer
      isAnalyze && visualizer({
        filename: 'dist/bundle-analysis.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap', // or 'sunburst', 'network'
      }),
      
      // Gzip compression
      isProduction && compression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240, // Only compress files > 10KB
        deleteOriginFile: false,
      }),
      
      // Brotli compression
      isProduction && compression({
        algorithm: 'brotliCompress',
        ext: '.br',
        threshold: 10240,
        deleteOriginFile: false,
      }),
      
      // Legacy browser support (optional, adds ~30KB)
      isProduction && process.env.LEGACY_SUPPORT && legacy({
        targets: ['defaults', 'not IE 11'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        renderLegacyChunks: true,
        polyfills: true,
      }),
    ].filter(Boolean),
    
    // ESBuild optimizations
    esbuild: {
      jsx: 'automatic',
      jsxDev: isDevelopment,
      // Remove console in production
      drop: isProduction ? ['console', 'debugger'] : [],
      // Minify identifiers
      minifyIdentifiers: isProduction,
      minifySyntax: isProduction,
      minifyWhitespace: isProduction,
      // Tree shaking
      treeShaking: true,
      // Pure annotations for better tree shaking
      pure: isProduction ? ['console.log', 'console.info'] : [],
    },
    
    build: {
      outDir: 'dist',
      sourcemap: isDevelopment ? false : 'hidden',
      
      // Use terser for better minification
      minify: isProduction ? 'terser' : false,
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info'],
          passes: 2,
          toplevel: true,
          ecma: 2020,
        },
        mangle: {
          toplevel: true,
          properties: {
            regex: /^_/,
          },
        },
        format: {
          comments: false,
          ecma: 2020,
        },
      },
      
      // Target modern browsers
      target: 'es2020',
      
      // Chunk size limits
      chunkSizeWarningLimit: 200, // Reduced from 400KB
      
      // Report compressed sizes
      reportCompressedSize: true,
      
      // CSS code splitting
      cssCodeSplit: true,
      
      // Inline small assets
      assetsInlineLimit: 4096, // 4KB
      
      rollupOptions: {
        // Keep all dependencies bundled for simplicity
        external: [],
        
        output: {
          
          // Optimize chunk names
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId ? 
              path.basename(chunkInfo.facadeModuleId) : 'chunk';
            return `assets/${facadeModuleId}-[hash].js`;
          },
          
          // Entry file names
          entryFileNames: 'assets/[name]-[hash].js',
          
          // Asset file names
          assetFileNames: 'assets/[name]-[hash].[ext]',
          
          // Advanced manual chunks for optimal splitting
          manualChunks: (id) => {
            // Node modules chunking
            if (id.includes('node_modules')) {
              // Critical vendor chunk (loaded immediately)
              if (id.includes('react-router') || 
                  id.includes('@tanstack/react-query')) {
                return 'vendor-critical';
              }
              
              // UI components (loaded after critical)
              if (id.includes('@radix-ui')) {
                // Split Radix UI by component size
                if (id.includes('dialog') || 
                    id.includes('dropdown') || 
                    id.includes('select')) {
                  return 'vendor-ui-complex';
                }
                return 'vendor-ui-base';
              }
              
              // Forms and validation
              if (id.includes('react-hook-form') || 
                  id.includes('zod') || 
                  id.includes('@hookform')) {
                return 'vendor-forms';
              }
              
              // Date utilities
              if (id.includes('date-fns')) {
                return 'vendor-date';
              }
              
              // Heavy export libraries (lazy loaded)
              if (id.includes('xlsx')) {
                return 'vendor-xlsx';
              }
              if (id.includes('jspdf')) {
                return 'vendor-pdf';
              }
              if (id.includes('jszip') || id.includes('file-saver')) {
                return 'vendor-compression';
              }
              
              // Image processing (lazy loaded)
              if (id.includes('jimp') || id.includes('react-image-crop')) {
                return 'vendor-image';
              }
              
              // Charts (lazy loaded)
              if (id.includes('recharts') || id.includes('d3')) {
                return 'vendor-charts';
              }
              
              // Animation
              if (id.includes('framer-motion')) {
                return 'vendor-animation';
              }
              
              // Icons
              if (id.includes('lucide-react')) {
                return 'vendor-icons';
              }
              
              // Network
              if (id.includes('axios') || id.includes('socket.io')) {
                return 'vendor-network';
              }
              
              // Utilities
              if (id.includes('clsx') || 
                  id.includes('class-variance-authority') ||
                  id.includes('tailwind-merge')) {
                return 'vendor-utils';
              }
              
              // Everything else
              return 'vendor-misc';
            }
            
            // Application code chunking
            
            // Split translations (lazy loaded per language)
            if (id.includes('/translations/') || id.includes('/locales/')) {
              const lang = id.match(/\/(en|zh|es|fr|de|ja|ko)\//)?.[1];
              return lang ? `lang-${lang}` : 'translations';
            }
            
            // Feature-based chunking
            if (id.includes('/pages/export/')) {
              return 'feature-export';
            }
            if (id.includes('/pages/segmentation/')) {
              return 'feature-segmentation';
            }
            if (id.includes('/pages/project/') || 
                id.includes('/components/project/')) {
              return 'feature-project';
            }
            if (id.includes('/pages/dashboard/') || 
                id.includes('/components/dashboard/')) {
              return 'feature-dashboard';
            }
            if (id.includes('/pages/settings/') || 
                id.includes('/components/settings/')) {
              return 'feature-settings';
            }
            
            // Shared services
            if (id.includes('/services/')) {
              if (id.includes('auth')) return 'services-auth';
              if (id.includes('api')) return 'services-api';
              return 'services-common';
            }
            
            // Utilities
            if (id.includes('/utils/')) {
              if (id.includes('validation')) return 'utils-validation';
              if (id.includes('format')) return 'utils-format';
              return 'utils-common';
            }
            
            // Hooks
            if (id.includes('/hooks/')) {
              return 'hooks';
            }
            
            // Components
            if (id.includes('/components/ui/')) {
              return 'components-ui';
            }
            if (id.includes('/components/')) {
              return 'components-common';
            }
            
            return undefined;
          },
        },
        
        // Tree shaking optimizations
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          tryCatchDeoptimization: false,
        },
      },
      
      // Module preload polyfill
      modulePreload: {
        polyfill: true,
      },
    },
    
    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
      ],
      exclude: [
        // Exclude heavy libraries that should be lazy loaded
        'xlsx',
        'jspdf',
        'jimp',
        'recharts',
      ],
      esbuildOptions: {
        target: 'es2020',
      },
    },
    
    // Resolve configuration
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@spheroseg/shared': path.resolve(__dirname, '../shared/src'),
        // Keep default React resolution for bundling
      },
      // Prefer ES modules
      mainFields: ['module', 'jsnext:main', 'jsnext', 'main'],
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
    },
    
    // Development server configuration
    server: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: true,
      hmr: {
        port: 3001,
        host: '0.0.0.0',
      },
    },
    
    // Preview server configuration
    preview: {
      port: 3000,
      strictPort: true,
      headers: {
        // Enable compression headers
        'Content-Encoding': 'gzip',
        // Security headers
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        // Cache headers for static assets
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    },
    
    // Define global constants
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      '__DEV__': JSON.stringify(!isProduction),
      '__PROD__': JSON.stringify(isProduction),
      // Feature flags
      '__ENABLE_PROFILING__': JSON.stringify(false),
      '__ENABLE_LEGACY_SUPPORT__': JSON.stringify(false),
    },
  };
});