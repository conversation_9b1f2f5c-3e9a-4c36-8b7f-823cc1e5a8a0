/**
 * Unified Monitoring System - Main Export
 * Consolidated monitoring utilities for consistent application-wide monitoring
 */

// Core monitoring components
export * from './core/MetricsRegistry';
export * from './core/MetricTypes';

// Metric collectors
export * from './collectors/HttpCollector';
export * from './collectors/DatabaseCollector';

// Legacy exports for backward compatibility
export * from './performanceMonitoring';
// Export legacy types with explicit names to avoid conflicts
export type { 
  MetricType,
  BaseMetric,
  Metric,
  PageLoadMetric,
  ComponentRenderMetric,
  ApiRequestMetric,
  ResourceLoadMetric,
  UserInteractionMetric,
  MemoryUsageMetric,
  ApiResponseTimeMetric,
  DatabaseQueryMetric,
  FileOperationMetric,
  MLInferenceMetric,
  MemoryHeapMetric,
  CPUUsageMetric
} from './metricsTypes';

// Re-export commonly used Prometheus types
export { Registry, Counter, Histogram, Gauge } from 'prom-client';

/**
 * Default exports for common use cases
 */
import { MetricsRegistry, defaultMetricsRegistry } from './core/MetricsRegistry';
import { HttpCollector } from './collectors/HttpCollector';
import { DatabaseCollector } from './collectors/DatabaseCollector';

/**
 * Create a complete monitoring setup with all collectors
 */
export function createMonitoringSystem(config?: {
  prefix?: string;
  collectDefaults?: boolean;
  defaultLabels?: { [key: string]: string };
}) {
  const registry = new MetricsRegistry(config);
  const httpCollector = new HttpCollector(registry);
  const databaseCollector = new DatabaseCollector(registry);

  return {
    registry,
    collectors: {
      http: httpCollector,
      database: databaseCollector,
    },
    middleware: {
      express: httpCollector.getExpressMiddleware(),
      database: databaseCollector.createQueryWrapper.bind(databaseCollector),
    },
  };
}

/**
 * Default monitoring system instance
 */
export const defaultMonitoringSystem = createMonitoringSystem({
  prefix: 'spheroseg_',
  collectDefaults: true,
  defaultLabels: {
    service: 'spheroseg',
  },
});

// Export default instances for convenience
export { defaultMetricsRegistry };
export const defaultHttpCollector = defaultMonitoringSystem.collectors.http;
export const defaultDatabaseCollector = defaultMonitoringSystem.collectors.database;