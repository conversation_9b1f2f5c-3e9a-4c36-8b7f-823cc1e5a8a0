/**
 * Database Metrics Collector
 * Standardized database operation metrics collection
 */

import { Counter, Histogram, Gauge } from 'prom-client';
import { MetricsRegistry } from '../core/MetricsRegistry';
import { STANDARD_METRICS, LABEL_NAMES, LABEL_VALUES, DB_DURATION_BUCKETS } from '../core/MetricTypes';

export interface DatabaseMetrics {
  queryDuration: Histogram;
  queryTotal: Counter;
  connectionsActive: Gauge;
  connectionsIdle: Gauge;
  connectionErrors: Counter;
  transactionDuration?: Histogram;
}

export interface DatabaseQueryData {
  operation: string;
  table: string;
  duration: number;
  success: boolean;
  rowCount?: number;
  querySize?: number;
  errorType?: string;
}

export interface DatabaseConnectionData {
  active: number;
  idle: number;
  waiting?: number;
  total?: number;
}

export class DatabaseCollector {
  private metrics: DatabaseMetrics;
  private registry: MetricsRegistry;

  constructor(registry: MetricsRegistry) {
    this.registry = registry;
    this.metrics = this.initializeMetrics();
  }

  private initializeMetrics(): DatabaseMetrics {
    const queryDuration = this.registry.getHistogram(
      STANDARD_METRICS.DB_QUERY_DURATION.name,
      STANDARD_METRICS.DB_QUERY_DURATION.help,
      [...STANDARD_METRICS.DB_QUERY_DURATION.labelNames, LABEL_NAMES.SUCCESS],
      STANDARD_METRICS.DB_QUERY_DURATION.buckets
    );

    const queryTotal = this.registry.getCounter(
      'db_queries_total',
      'Total number of database queries',
      [LABEL_NAMES.OPERATION, LABEL_NAMES.TABLE, LABEL_NAMES.SUCCESS]
    );

    const connectionsActive = this.registry.getGauge(
      'database_connections_active',
      'Number of active database connections',
      []
    );

    const connectionsIdle = this.registry.getGauge(
      'database_connections_idle',
      'Number of idle database connections',
      []
    );

    const connectionErrors = this.registry.getCounter(
      'database_connection_errors_total',
      'Total number of database connection errors',
      [LABEL_NAMES.ERROR_TYPE]
    );

    // Optional transaction metrics
    const transactionDuration = this.registry.getHistogram(
      'db_transaction_duration_seconds',
      'Duration of database transactions in seconds',
      [LABEL_NAMES.OPERATION, LABEL_NAMES.SUCCESS],
      DB_DURATION_BUCKETS
    );

    return {
      queryDuration,
      queryTotal,
      connectionsActive,
      connectionsIdle,
      connectionErrors,
      transactionDuration,
    };
  }

  /**
   * Record a database query
   */
  recordQuery(data: DatabaseQueryData): void {
    const successLabel = data.success.toString();

    // Record query duration
    this.metrics.queryDuration
      .labels(data.operation, data.table, successLabel)
      .observe(data.duration);

    // Record query count
    this.metrics.queryTotal
      .labels(data.operation, data.table, successLabel)
      .inc();

    // Record query size if provided
    if (data.querySize) {
      const querySizeMetric = this.registry.getHistogram(
        'db_query_size_bytes',
        'Size of database queries in bytes',
        [LABEL_NAMES.OPERATION, LABEL_NAMES.TABLE],
        [100, 1000, 10000, 100000, 1000000]
      );

      querySizeMetric
        .labels(data.operation, data.table)
        .observe(data.querySize);
    }

    // Record row count if provided
    if (data.rowCount !== undefined) {
      const rowCountMetric = this.registry.getHistogram(
        'db_query_rows',
        'Number of rows affected by database queries',
        [LABEL_NAMES.OPERATION, LABEL_NAMES.TABLE],
        [1, 10, 100, 1000, 10000, 100000]
      );

      rowCountMetric
        .labels(data.operation, data.table)
        .observe(data.rowCount);
    }

    // Record errors
    if (!data.success && data.errorType) {
      const errorMetric = this.registry.getCounter(
        'db_query_errors_total',
        'Total number of database query errors',
        [LABEL_NAMES.ERROR_TYPE, LABEL_NAMES.OPERATION, LABEL_NAMES.TABLE]
      );

      errorMetric
        .labels(data.errorType, data.operation, data.table)
        .inc();
    }
  }

  /**
   * Update connection pool metrics
   */
  updateConnections(data: DatabaseConnectionData): void {
    this.metrics.connectionsActive.set(data.active);
    this.metrics.connectionsIdle.set(data.idle);

    // Update waiting connections if provided
    if (data.waiting !== undefined) {
      const waitingMetric = this.registry.getGauge(
        'database_connections_waiting',
        'Number of waiting database connections',
        []
      );
      waitingMetric.set(data.waiting);
    }

    // Update total connections if provided
    if (data.total !== undefined) {
      const totalMetric = this.registry.getGauge(
        'database_connections_total',
        'Total number of database connections',
        []
      );
      totalMetric.set(data.total);
    }
  }

  /**
   * Record a connection error
   */
  recordConnectionError(errorType: string): void {
    this.metrics.connectionErrors
      .labels(errorType)
      .inc();
  }

  /**
   * Record a transaction
   */
  recordTransaction(operation: string, duration: number, success: boolean): void {
    if (this.metrics.transactionDuration) {
      this.metrics.transactionDuration
        .labels(operation, success.toString())
        .observe(duration);
    }

    // Also record transaction count
    const transactionCountMetric = this.registry.getCounter(
      'db_transactions_total',
      'Total number of database transactions',
      [LABEL_NAMES.OPERATION, LABEL_NAMES.SUCCESS]
    );

    transactionCountMetric
      .labels(operation, success.toString())
      .inc();
  }

  /**
   * Create a query wrapper that automatically records metrics
   */
  createQueryWrapper<T = any>(originalQuery: Function) {
    return async (query: string, params?: any[], options?: { 
      operation?: string; 
      table?: string; 
    }): Promise<T> => {
      const startTime = Date.now();
      const operation = options?.operation || this.extractOperation(query);
      const table = options?.table || this.extractTable(query);

      try {
        const result = await originalQuery(query, params);
        const duration = (Date.now() - startTime) / 1000;

        this.recordQuery({
          operation,
          table,
          duration,
          success: true,
          querySize: Buffer.byteLength(query, 'utf8'),
          rowCount: result?.rowCount || result?.length,
        });

        return result;
      } catch (error) {
        const duration = (Date.now() - startTime) / 1000;
        
        this.recordQuery({
          operation,
          table,
          duration,
          success: false,
          querySize: Buffer.byteLength(query, 'utf8'),
          errorType: this.classifyError(error),
        });

        throw error;
      }
    };
  }

  /**
   * Extract operation type from SQL query
   */
  private extractOperation(query: string): string {
    const normalized = query.trim().toLowerCase();
    
    if (normalized.startsWith('select')) return 'SELECT';
    if (normalized.startsWith('insert')) return 'INSERT';
    if (normalized.startsWith('update')) return 'UPDATE';
    if (normalized.startsWith('delete')) return 'DELETE';
    if (normalized.startsWith('create')) return 'CREATE';
    if (normalized.startsWith('alter')) return 'ALTER';
    if (normalized.startsWith('drop')) return 'DROP';
    if (normalized.startsWith('with')) return 'SELECT'; // CTE queries
    
    return 'OTHER';
  }

  /**
   * Extract table name from SQL query
   */
  private extractTable(query: string): string {
    const normalized = query.trim().toLowerCase();
    
    // Simple pattern matching for table names
    let match = normalized.match(/from\s+["`]?(\w+)["`]?/);
    if (match) return match[1];
    
    match = normalized.match(/into\s+["`]?(\w+)["`]?/);
    if (match) return match[1];
    
    match = normalized.match(/update\s+["`]?(\w+)["`]?/);
    if (match) return match[1];
    
    match = normalized.match(/table\s+["`]?(\w+)["`]?/);
    if (match) return match[1];
    
    return 'unknown';
  }

  /**
   * Classify database error types
   */
  private classifyError(error: any): string {
    if (!error) return 'unknown';
    
    const message = error.message?.toLowerCase() || '';
    const code = error.code || '';
    
    // Connection errors
    if (message.includes('connection') || code === 'ECONNREFUSED') {
      return 'connection_error';
    }
    
    // Timeout errors
    if (message.includes('timeout') || code === 'ETIMEDOUT') {
      return 'timeout_error';
    }
    
    // Constraint violations
    if (message.includes('constraint') || message.includes('unique')) {
      return 'constraint_violation';
    }
    
    // Syntax errors
    if (message.includes('syntax') || message.includes('parse')) {
      return 'syntax_error';
    }
    
    // Permission errors
    if (message.includes('permission') || message.includes('access denied')) {
      return 'permission_error';
    }
    
    return 'query_error';
  }

  /**
   * Get current metrics snapshot for debugging
   */
  getMetricsSnapshot(): any {
    return {
      activeConnections: 'gauge_metric',
      idleConnections: 'gauge_metric',
      queryDuration: 'histogram',
      queryTotal: 'counter',
    };
  }

  /**
   * Monitor connection pool health
   */
  monitorConnectionHealth(): { status: string; details: any } {
    // Simple health check based on metrics availability
    let status = 'healthy';
    const details: any = { 
      active: 'metrics_available',
      idle: 'metrics_available',
      total: 'calculated_from_metrics'
    };

    return { status, details };
  }
}