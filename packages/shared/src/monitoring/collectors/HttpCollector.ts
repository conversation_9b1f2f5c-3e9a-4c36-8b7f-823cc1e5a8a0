/**
 * HTTP Metrics Collector
 * Standardized HTTP request/response metrics collection
 */

import { Counter, Histogram, Gauge } from 'prom-client';
import { MetricsRegistry } from '../core/MetricsRegistry';
import { STANDARD_METRICS, LABEL_NAMES, LABEL_VALUES, getErrorType, cleanRoute, PrometheusLabels } from '../core/MetricTypes';

export interface HttpMetrics {
  requestsTotal: Counter;
  requestDuration: Histogram;
  activeConnections: Gauge;
  requestSize?: Histogram;
  responseSize?: Histogram;
}

export interface HttpRequestData {
  method: string;
  route: string;
  statusCode: number;
  duration: number;
  requestSize?: number;
  responseSize?: number;
  userId?: string;
}

export class HttpCollector {
  private metrics: HttpMetrics;
  private registry: MetricsRegistry;

  constructor(registry: MetricsRegistry) {
    this.registry = registry;
    this.metrics = this.initializeMetrics();
  }

  private initializeMetrics(): HttpMetrics {
    const requestsTotal = this.registry.getCounter(
      STANDARD_METRICS.HTTP_REQUESTS.name,
      STANDARD_METRICS.HTTP_REQUESTS.help,
      STANDARD_METRICS.HTTP_REQUESTS.labelNames
    );

    const requestDuration = this.registry.getHistogram(
      STANDARD_METRICS.HTTP_DURATION.name,
      STANDARD_METRICS.HTTP_DURATION.help,
      STANDARD_METRICS.HTTP_DURATION.labelNames,
      STANDARD_METRICS.HTTP_DURATION.buckets
    );

    const activeConnections = this.registry.getGauge(
      STANDARD_METRICS.ACTIVE_CONNECTIONS.name,
      STANDARD_METRICS.ACTIVE_CONNECTIONS.help,
      STANDARD_METRICS.ACTIVE_CONNECTIONS.labelNames
    );

    // Optional request/response size metrics
    const requestSize = this.registry.getHistogram(
      'http_request_size_bytes',
      'Size of HTTP requests in bytes',
      [LABEL_NAMES.METHOD, LABEL_NAMES.ROUTE],
      [100, 1000, 10000, 100000, 1000000, 10000000]
    );

    const responseSize = this.registry.getHistogram(
      'http_response_size_bytes',
      'Size of HTTP responses in bytes',
      [LABEL_NAMES.METHOD, LABEL_NAMES.ROUTE, LABEL_NAMES.STATUS_CODE],
      [100, 1000, 10000, 100000, 1000000, 10000000]
    );

    return {
      requestsTotal,
      requestDuration,
      activeConnections,
      requestSize,
      responseSize,
    };
  }

  /**
   * Record an HTTP request
   */
  recordRequest(data: HttpRequestData): void {
    const cleanedRoute = cleanRoute(data.route);
    const statusCode = data.statusCode.toString();

    // Record request count
    this.metrics.requestsTotal
      .labels(data.method, cleanedRoute, statusCode)
      .inc();

    // Record request duration
    this.metrics.requestDuration
      .labels(data.method, cleanedRoute, statusCode)
      .observe(data.duration);

    // Record request size if provided
    if (data.requestSize && this.metrics.requestSize) {
      this.metrics.requestSize
        .labels(data.method, cleanedRoute)
        .observe(data.requestSize);
    }

    // Record response size if provided
    if (data.responseSize && this.metrics.responseSize) {
      this.metrics.responseSize
        .labels(data.method, cleanedRoute, statusCode)
        .observe(data.responseSize);
    }
  }

  /**
   * Record an error
   */
  recordError(method: string, route: string, statusCode: number, errorType?: string): void {
    const errorMetric = this.registry.getCounter(
      STANDARD_METRICS.ERRORS.name,
      STANDARD_METRICS.ERRORS.help,
      STANDARD_METRICS.ERRORS.labelNames
    );

    const cleanedRoute = cleanRoute(route);
    const detectedErrorType = errorType || getErrorType(statusCode);

    errorMetric
      .labels(detectedErrorType, statusCode.toString())
      .inc();
  }

  /**
   * Update active connections count
   */
  setActiveConnections(count: number): void {
    this.metrics.activeConnections.set(count);
  }

  /**
   * Increment active connections
   */
  incrementActiveConnections(): void {
    this.metrics.activeConnections.inc();
  }

  /**
   * Decrement active connections
   */
  decrementActiveConnections(): void {
    this.metrics.activeConnections.dec();
  }

  /**
   * Get middleware for Express.js
   */
  getExpressMiddleware() {
    return (req: any, res: any, next: any) => {
      const startTime = Date.now();

      // Increment active connections
      this.incrementActiveConnections();

      // Get original end method
      const originalEnd = res.end;

      // Override end method to capture metrics
      res.end = function(this: any, chunk?: any, encoding?: BufferEncoding, cb?: () => void) {
        const duration = (Date.now() - startTime) / 1000;
        const route = req.route?.path || req.path || req.url;

        // Calculate sizes if available
        const requestSize = req.get('content-length') ? parseInt(req.get('content-length'), 10) : undefined;
        const responseSize = chunk ? Buffer.byteLength(chunk) : undefined;

        // Record the request
        this.recordRequest({
          method: req.method,
          route,
          statusCode: res.statusCode,
          duration,
          requestSize,
          responseSize,
          userId: req.user?.id,
        });

        // Record error if status code indicates error
        if (res.statusCode >= 400) {
          this.recordError(req.method, route, res.statusCode);
        }

        // Decrement active connections
        this.decrementActiveConnections();

        // Call original end method
        if (encoding && cb) {
          return originalEnd.call(this, chunk, encoding, cb);
        } else if (encoding) {
          return originalEnd.call(this, chunk, encoding);
        } else if (chunk) {
          return originalEnd.call(this, chunk);
        } else {
          return originalEnd.call(this);
        }
      }.bind(this);

      next();
    };
  }

  /**
   * Record API-specific metrics with additional context
   */
  recordApiRequest(data: HttpRequestData & {
    endpoint: string;
    success: boolean;
    errorMessage?: string;
  }): void {
    // Record standard HTTP request
    this.recordRequest(data);

    // Record API-specific metrics
    const apiMetric = this.registry.getHistogram(
      'api_response_time_seconds',
      'API response time in seconds',
      [LABEL_NAMES.ENDPOINT, LABEL_NAMES.METHOD, LABEL_NAMES.SUCCESS],
      [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
    );

    apiMetric
      .labels(data.endpoint, data.method, data.success.toString())
      .observe(data.duration);

    // Record API errors with more context
    if (!data.success && data.errorMessage) {
      const apiErrorMetric = this.registry.getCounter(
        'api_errors_total',
        'Total number of API errors',
        [LABEL_NAMES.ENDPOINT, LABEL_NAMES.METHOD, LABEL_NAMES.ERROR_TYPE]
      );

      const errorType = getErrorType(data.statusCode);
      apiErrorMetric
        .labels(data.endpoint, data.method, errorType)
        .inc();
    }
  }

  /**
   * Get current metrics values for debugging
   */
  getMetricsSnapshot(): any {
    return {
      requestsTotal: 'counter',
      requestDuration: 'histogram',
      activeConnections: 'gauge_metric',
    };
  }
}