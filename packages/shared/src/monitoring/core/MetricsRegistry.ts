/**
 * Unified Metrics Registry
 * Single source of truth for Prometheus metrics across frontend and backend
 */

import { Registry, Counter, Histogram, Gauge, collectDefaultMetrics } from 'prom-client';

export interface RegistryLabels {
  [key: string]: string;
}

export interface MetricsConfig {
  prefix: string;
  collectDefaults: boolean;
  defaultLabels?: RegistryLabels;
}

export class MetricsRegistry {
  private registry: Registry;
  private config: MetricsConfig;
  private metrics = new Map<string, Counter | Histogram | Gauge>();

  constructor(config: Partial<MetricsConfig> = {}) {
    this.config = {
      prefix: 'spheroseg_',
      collectDefaults: true,
      ...config,
    };

    this.registry = new Registry();

    // Set default labels if provided
    if (this.config.defaultLabels) {
      this.registry.setDefaultLabels(this.config.defaultLabels);
    }

    // Collect default system metrics
    if (this.config.collectDefaults) {
      collectDefaultMetrics({
        register: this.registry,
        prefix: this.config.prefix,
      });
    }
  }

  /**
   * Get the underlying Prometheus registry
   */
  getRegistry(): Registry {
    return this.registry;
  }

  /**
   * Create or get a Counter metric
   */
  getCounter(
    name: string,
    help: string,
    labelNames: string[] = []
  ): Counter {
    const metricName = this.getMetricName(name);
    
    let metric = this.metrics.get(metricName);
    if (!metric) {
      metric = new Counter({
        name: metricName,
        help,
        labelNames,
        registers: [this.registry],
      });
      this.metrics.set(metricName, metric);
    }
    
    return metric as Counter;
  }

  /**
   * Create or get a Histogram metric
   */
  getHistogram(
    name: string,
    help: string,
    labelNames: string[] = [],
    buckets?: number[]
  ): Histogram {
    const metricName = this.getMetricName(name);
    
    let metric = this.metrics.get(metricName);
    if (!metric) {
      const config: any = {
        name: metricName,
        help,
        labelNames,
        registers: [this.registry],
      };
      
      if (buckets) {
        config.buckets = buckets;
      }
      
      metric = new Histogram(config);
      this.metrics.set(metricName, metric);
    }
    
    return metric as Histogram;
  }

  /**
   * Create or get a Gauge metric
   */
  getGauge(
    name: string,
    help: string,
    labelNames: string[] = []
  ): Gauge {
    const metricName = this.getMetricName(name);
    
    let metric = this.metrics.get(metricName);
    if (!metric) {
      metric = new Gauge({
        name: metricName,
        help,
        labelNames,
        registers: [this.registry],
      });
      this.metrics.set(metricName, metric);
    }
    
    return metric as Gauge;
  }

  /**
   * Get metrics as string for /metrics endpoint
   */
  async getMetrics(): Promise<string> {
    return await this.registry.metrics();
  }

  /**
   * Get metrics content type
   */
  getContentType(): string {
    return this.registry.contentType;
  }

  /**
   * Clear all metrics (useful for testing)
   */
  clear(): void {
    this.registry.clear();
    this.metrics.clear();
  }

  /**
   * Get metric summary for debugging
   */
  getMetricsSummary(): { [key: string]: string } {
    const summary: { [key: string]: string } = {};
    
    for (const [name] of this.metrics) {
      summary[name] = 'registered';
    }
    
    return summary;
  }

  /**
   * Generate full metric name with prefix
   */
  private getMetricName(name: string): string {
    // Remove prefix if already present to avoid duplication
    const baseName = name.startsWith(this.config.prefix) 
      ? name.slice(this.config.prefix.length) 
      : name;
    
    return `${this.config.prefix}${baseName}`;
  }

  /**
   * Get all registered metric names
   */
  getRegisteredMetrics(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Check if metric exists
   */
  hasMetric(name: string): boolean {
    const metricName = this.getMetricName(name);
    return this.metrics.has(metricName);
  }

  /**
   * Remove a metric from registry
   */
  removeMetric(name: string): boolean {
    const metricName = this.getMetricName(name);
    const metric = this.metrics.get(metricName);
    
    if (metric) {
      this.registry.removeSingleMetric(metricName);
      this.metrics.delete(metricName);
      return true;
    }
    
    return false;
  }
}

// Export default instance
export const defaultMetricsRegistry = new MetricsRegistry();