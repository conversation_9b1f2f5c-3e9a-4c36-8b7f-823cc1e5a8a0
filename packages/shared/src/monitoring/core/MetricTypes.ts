/**
 * Unified Metric Type Definitions
 * Standardized metric definitions for consistent monitoring
 */

export interface PrometheusLabels {
  [key: string]: string;
}

/**
 * Standard HTTP buckets for response time measurements
 */
export const HTTP_DURATION_BUCKETS = [0.001, 0.005, 0.015, 0.05, 0.1, 0.5, 1, 2, 5, 10];

/**
 * Standard database query duration buckets
 */
export const DB_DURATION_BUCKETS = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5];

/**
 * Standard file size buckets (in bytes)
 */
export const FILE_SIZE_BUCKETS = [1024, 10240, 102400, 1048576, 10485760, 104857600]; // 1KB to 100MB

/**
 * Standard ML processing duration buckets
 */
export const ML_DURATION_BUCKETS = [1, 5, 10, 30, 60, 120, 300, 600];

/**
 * Standard queue processing duration buckets
 */
export const QUEUE_DURATION_BUCKETS = [0.1, 0.5, 1, 5, 10, 30, 60];

/**
 * Standard metric name patterns for consistency
 */
export const METRIC_NAMES = {
  // HTTP metrics
  HTTP_REQUESTS_TOTAL: 'http_requests_total',
  HTTP_REQUEST_DURATION: 'http_request_duration_seconds',
  HTTP_REQUEST_SIZE: 'http_request_size_bytes',
  HTTP_RESPONSE_SIZE: 'http_response_size_bytes',
  
  // Application metrics
  ACTIVE_CONNECTIONS: 'active_connections',
  USER_SESSIONS_ACTIVE: 'user_sessions_active',
  
  // Business metrics
  PROJECTS_CREATED_TOTAL: 'projects_created_total',
  IMAGES_PROCESSED_TOTAL: 'images_processed_total',
  IMAGES_UPLOADED_TOTAL: 'images_uploaded_total',
  
  // Database metrics
  DB_CONNECTIONS_ACTIVE: 'database_connections_active',
  DB_CONNECTIONS_IDLE: 'database_connections_idle',
  DB_QUERY_DURATION: 'db_query_duration_seconds',
  DB_QUERY_TOTAL: 'db_queries_total',
  
  // File operation metrics
  UPLOAD_SIZE_BYTES: 'upload_size_bytes',
  UPLOAD_DURATION: 'upload_duration_seconds',
  FILE_OPERATIONS_TOTAL: 'file_operations_total',
  
  // ML service metrics
  ML_PROCESSING_DURATION: 'ml_processing_duration_seconds',
  ML_REQUESTS_TOTAL: 'ml_requests_total',
  ML_MODEL_LOAD_DURATION: 'ml_model_load_duration_seconds',
  
  // Queue metrics
  QUEUE_SIZE: 'queue_size',
  QUEUE_PROCESSING_DURATION: 'queue_processing_duration_seconds',
  QUEUE_JOBS_TOTAL: 'queue_jobs_total',
  
  // Cache metrics
  CACHE_HITS_TOTAL: 'cache_hits_total',
  CACHE_MISSES_TOTAL: 'cache_misses_total',
  CACHE_SIZE: 'cache_size',
  
  // Error metrics
  ERRORS_TOTAL: 'errors_total',
  ERROR_RATE: 'error_rate',
  
  // System metrics
  MEMORY_USAGE_BYTES: 'memory_usage_bytes',
  CPU_USAGE_PERCENT: 'cpu_usage_percent',
  
  // Authentication metrics
  LOGIN_ATTEMPTS_TOTAL: 'login_attempts_total',
  USER_REGISTRATIONS_TOTAL: 'user_registrations_total',
  PASSWORD_RESETS_TOTAL: 'password_resets_total',
} as const;

/**
 * Standard label names for consistency
 */
export const LABEL_NAMES = {
  METHOD: 'method',
  ROUTE: 'route',
  STATUS_CODE: 'status_code',
  USER_ID: 'user_id',
  PROJECT_ID: 'project_id',
  IMAGE_ID: 'image_id',
  MODEL: 'model',
  TABLE: 'table',
  OPERATION: 'operation',
  QUEUE_NAME: 'queue_name',
  JOB_TYPE: 'job_type',
  CACHE_NAME: 'cache_name',
  ERROR_TYPE: 'error_type',
  ERROR_CODE: 'error_code',
  SEVERITY: 'severity',
  SUCCESS: 'success',
  TYPE: 'type',
  STATE: 'state',
  ENDPOINT: 'endpoint',
  COMPONENT: 'component',
} as const;

/**
 * Common label value patterns
 */
export const LABEL_VALUES = {
  SUCCESS: {
    TRUE: 'true',
    FALSE: 'false',
  },
  SEVERITY: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical',
  },
  ERROR_TYPE: {
    CLIENT_ERROR: 'client_error',
    SERVER_ERROR: 'server_error',
    VALIDATION_ERROR: 'validation_error',
    AUTHENTICATION_ERROR: 'authentication_error',
    AUTHORIZATION_ERROR: 'authorization_error',
    NOT_FOUND: 'not_found',
    TIMEOUT: 'timeout',
    DATABASE_ERROR: 'database_error',
    ML_ERROR: 'ml_error',
    FILE_ERROR: 'file_error',
  },
  DB_STATE: {
    ACTIVE: 'active',
    IDLE: 'idle',
    WAITING: 'waiting',
  },
  CACHE_OPERATION: {
    GET: 'get',
    SET: 'set',
    DELETE: 'delete',
    FLUSH: 'flush',
  },
} as const;

/**
 * Metric configuration interface
 */
export interface MetricDefinition {
  name: string;
  help: string;
  labelNames: string[];
  buckets?: number[];
}

/**
 * Standard metric definitions
 */
export const STANDARD_METRICS: { [key: string]: MetricDefinition } = {
  HTTP_REQUESTS: {
    name: METRIC_NAMES.HTTP_REQUESTS_TOTAL,
    help: 'Total number of HTTP requests',
    labelNames: [LABEL_NAMES.METHOD, LABEL_NAMES.ROUTE, LABEL_NAMES.STATUS_CODE],
  },
  
  HTTP_DURATION: {
    name: METRIC_NAMES.HTTP_REQUEST_DURATION,
    help: 'Duration of HTTP requests in seconds',
    labelNames: [LABEL_NAMES.METHOD, LABEL_NAMES.ROUTE, LABEL_NAMES.STATUS_CODE],
    buckets: HTTP_DURATION_BUCKETS,
  },
  
  DB_QUERY_DURATION: {
    name: METRIC_NAMES.DB_QUERY_DURATION,
    help: 'Duration of database queries in seconds',
    labelNames: [LABEL_NAMES.OPERATION, LABEL_NAMES.TABLE],
    buckets: DB_DURATION_BUCKETS,
  },
  
  UPLOAD_SIZE: {
    name: METRIC_NAMES.UPLOAD_SIZE_BYTES,
    help: 'Size of uploaded files in bytes',
    labelNames: [LABEL_NAMES.TYPE],
    buckets: FILE_SIZE_BUCKETS,
  },
  
  ML_PROCESSING: {
    name: METRIC_NAMES.ML_PROCESSING_DURATION,
    help: 'Duration of ML processing in seconds',
    labelNames: [LABEL_NAMES.MODEL, LABEL_NAMES.SUCCESS],
    buckets: ML_DURATION_BUCKETS,
  },
  
  ERRORS: {
    name: METRIC_NAMES.ERRORS_TOTAL,
    help: 'Total number of errors by type',
    labelNames: [LABEL_NAMES.ERROR_TYPE, LABEL_NAMES.ERROR_CODE],
  },
  
  ACTIVE_CONNECTIONS: {
    name: METRIC_NAMES.ACTIVE_CONNECTIONS,
    help: 'Number of active connections',
    labelNames: [],
  },
  
  DB_CONNECTIONS: {
    name: METRIC_NAMES.DB_CONNECTIONS_ACTIVE,
    help: 'Number of database connections by state',
    labelNames: [LABEL_NAMES.STATE],
  },
  
  QUEUE_SIZE: {
    name: METRIC_NAMES.QUEUE_SIZE,
    help: 'Current queue size',
    labelNames: [LABEL_NAMES.QUEUE_NAME],
  },
} as const;

/**
 * Helper function to create consistent metric labels
 */
export function createLabels(labels: { [key: string]: string | number }): PrometheusLabels {
  const result: PrometheusLabels = {};
  
  for (const [key, value] of Object.entries(labels)) {
    result[key] = String(value);
  }
  
  return result;
}

/**
 * Helper function to get standard error type based on HTTP status code
 */
export function getErrorType(statusCode: number): string {
  if (statusCode >= 400 && statusCode < 500) {
    switch (statusCode) {
      case 401:
        return LABEL_VALUES.ERROR_TYPE.AUTHENTICATION_ERROR;
      case 403:
        return LABEL_VALUES.ERROR_TYPE.AUTHORIZATION_ERROR;
      case 404:
        return LABEL_VALUES.ERROR_TYPE.NOT_FOUND;
      case 408:
        return LABEL_VALUES.ERROR_TYPE.TIMEOUT;
      default:
        return LABEL_VALUES.ERROR_TYPE.CLIENT_ERROR;
    }
  } else if (statusCode >= 500) {
    return LABEL_VALUES.ERROR_TYPE.SERVER_ERROR;
  }
  
  return 'unknown';
}

/**
 * Helper function to clean route paths for consistent labeling
 */
export function cleanRoute(path: string): string {
  // Replace path parameters with generic placeholders
  return path
    .replace(/\/\d+/g, '/:id')           // Replace numeric IDs
    .replace(/\/:[^/]+/g, '/:param')     // Replace named parameters
    .replace(/\/+/g, '/')                // Remove double slashes
    .replace(/\/$/, '') || '/';          // Remove trailing slash
}