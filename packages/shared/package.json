{"name": "@spheroseg/shared", "version": "1.0.0", "type": "module", "description": "Shared utilities for SpheroSeg application", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./src/api/responseHandler": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./utils/imageUtils": {"types": "./dist/shared/src/utils/imageUtils.d.ts", "default": "./dist/shared/src/utils/imageUtils.js"}, "./utils/pathUtils": {"types": "./dist/shared/src/utils/pathUtils.d.ts", "default": "./dist/shared/src/utils/pathUtils.js"}, "./utils/polygonWasmUtils": {"types": "./dist/shared/src/utils/polygonWasmUtils.d.ts", "default": "./dist/shared/src/utils/polygonWasmUtils.js"}, "./utils/polygonUtils": {"types": "./dist/shared/src/utils/polygonUtils.d.ts", "default": "./dist/shared/src/utils/polygonUtils.js"}, "./services/upload": {"types": "./dist/shared/src/services/upload/index.d.ts", "default": "./dist/shared/src/services/upload/index.js"}, "./utils/editModeManager": {"types": "./dist/shared/src/utils/editModeManager.d.ts", "default": "./dist/shared/src/utils/editModeManager.js"}, "./utils/polygonEventHandlers": {"types": "./dist/shared/src/utils/polygonEventHandlers.d.ts", "default": "./dist/shared/src/utils/polygonEventHandlers.js"}, "./src/monitoring": {"types": "./dist/shared/src/monitoring/index.d.ts", "default": "./dist/shared/src/monitoring/index.js"}, "./src/utils/unifiedErrorSystem": {"types": "./dist/utils/unifiedErrorSystem.d.ts", "default": "./dist/utils/unifiedErrorSystem.js"}}, "scripts": {"build": "tsc && ./fix-imports.sh", "build:prod": "NODE_ENV=production tsc && ./fix-imports.sh", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "type-check:strict": "tsc --noEmit --project ../../tsconfig.strict.json --incremental --tsBuildInfoFile .cache/tsconfig.strict.tsbuildinfo", "lint": "echo 'No linting configured for shared package'", "lint:fix": "echo 'No linting configured for shared package'", "format": "echo 'No formatting configured for shared package'", "format:check": "echo 'No formatting configured for shared package'", "test": "echo 'No tests configured for shared package'", "test:unit": "echo 'No tests configured for shared package'", "test:integration": "echo 'No tests configured for shared package'", "test:coverage": "echo 'No tests configured for shared package'", "test:ci": "echo 'No tests configured for shared package'", "test:watch": "echo 'No tests configured for shared package'", "code:check": "npm run type-check", "code:fix": "npm run type-check"}, "dependencies": {"@types/uuid": "^10.0.0", "isomorphic-dompurify": "^2.26.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/dompurify": "^3.0.5", "typescript": "^5.0.0"}}