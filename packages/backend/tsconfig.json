{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "types": ["node", "vitest/globals"],
    "outDir": "dist",
    "noEmit": false,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    // Incremental compilation for faster builds
    "incremental": true,
    "tsBuildInfoFile": ".cache/tsconfig.tsbuildinfo",
    // Build enforcement - CRITICAL: fail build on TypeScript errors
    "noEmitOnError": true,
    // Gradual strict mode migration - enable progressively
    "strict": false,
    "noImplicitAny": true,
    "strictNullChecks": true
  },
  "include": [
    "src/**/*.ts",
    "generated/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/__tests__/**",
    "**/tests/**",
    "**/test-utils/**",
    "src/test-utils/**",
    "src/test/**",
    "src/scripts/**/*"
  ]
}