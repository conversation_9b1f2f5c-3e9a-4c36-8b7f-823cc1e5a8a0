import { v4 as uuidv4 } from 'uuid';
import { ApiError } from '../utils/errors';
import logger from '../utils/logger';
import { broadcastSegmentationProgress } from './socketService';
import { prisma } from './PrismaService';

export interface SegmentationJob {
  jobId: string;
  imageId: string;
  projectId?: string; // Add project ID for WebSocket broadcasting
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100 percentage
  stage?: string; // Current processing stage
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  options?: any;
}

export interface JobStatus {
  jobId: string;
  status: string;
  progress?: number;
  result?: any;
  error?: string;
}

export interface QueueStatus {
  pendingTasks: number;
  activeTasks: number;
  completedTasks: number;
  failedTasks: number;
}

class SegmentationQueueService {
  private jobs: Map<string, SegmentationJob> = new Map();
  private queue: string[] = [];

  /**
   * Queue a segmentation job
   */
  async queueSegmentationJob(imageId: string, options: any = {}): Promise<SegmentationJob> {
    const jobId = uuidv4();
    
    // Get project ID from image
    let projectId: string | undefined;
    try {
      const image = await prisma.image.findUnique({
        where: { id: imageId },
        select: { projectId: true }
      });
      projectId = image?.projectId || undefined;
    } catch (error) {
      logger.warn(`Could not get project ID for image ${imageId}:`, error);
    }
    
    const job: SegmentationJob = {
      jobId,
      imageId,
      projectId,
      status: 'queued',
      progress: 0,
      stage: 'Queued for processing',
      createdAt: new Date(),
      updatedAt: new Date(),
      options,
    };

    this.jobs.set(jobId, job);
    this.queue.push(jobId);

    logger.info(`Segmentation job ${jobId} queued for image ${imageId}`);

    // Emit progress event for queued status
    if (projectId) {
      broadcastSegmentationProgress(projectId, imageId, {
        status: 'queued',
        progress: 0,
        stage: 'Queued for processing',
        startedAt: job.createdAt.toISOString(),
      });
    }

    // Simulate processing (in real implementation, this would be sent to RabbitMQ)
    setTimeout(() => this.processJob(jobId), 100);

    return job;
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<JobStatus> {
    const job = this.jobs.get(jobId);

    if (!job) {
      throw new ApiError('Job not found', 404);
    }

    return {
      jobId: job.jobId,
      status: job.status,
      progress: job.progress,
    };
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<SegmentationJob> {
    const job = this.jobs.get(jobId);

    if (!job) {
      throw new ApiError('Job not found', 404);
    }

    if (job.status === 'completed' || job.status === 'failed') {
      throw new ApiError('Cannot cancel completed job', 400);
    }

    job.status = 'cancelled';
    job.updatedAt = new Date();

    // Remove from queue if present
    const queueIndex = this.queue.indexOf(jobId);
    if (queueIndex > -1) {
      this.queue.splice(queueIndex, 1);
    }

    logger.info(`Job ${jobId} cancelled`);
    return job;
  }

  /**
   * Add task to queue (compatibility method)
   */
  async addTask(
    imageId: string,
    imagePath: string,
    parameters: any = {},
    priority: number = 5
  ): Promise<string> {
    const job = await this.queueSegmentationJob(imageId, {
      imagePath,
      parameters,
      priority,
    });
    return job.jobId;
  }

  /**
   * Trigger segmentation task (compatibility method)
   */
  async triggerSegmentationTask(
    imageId: string,
    imagePath: string,
    parameters: any = {},
    priority: number = 5
  ): Promise<string> {
    return this.addTask(imageId, imagePath, parameters, priority);
  }

  /**
   * Get queue status
   */
  async getQueueStatus(): Promise<QueueStatus> {
    let pendingTasks = 0;
    let activeTasks = 0;
    let completedTasks = 0;
    let failedTasks = 0;

    for (const job of this.jobs.values()) {
      switch (job.status) {
        case 'queued':
          pendingTasks++;
          break;
        case 'processing':
          activeTasks++;
          break;
        case 'completed':
          completedTasks++;
          break;
        case 'failed':
          failedTasks++;
          break;
      }
    }

    return {
      pendingTasks,
      activeTasks,
      completedTasks,
      failedTasks,
    };
  }

  /**
   * Get segmentation queue status (compatibility method)
   */
  async getSegmentationQueueStatus(): Promise<QueueStatus> {
    return this.getQueueStatus();
  }

  /**
   * Cancel task (compatibility method)
   */
  async cancelTask(imageId: string): Promise<boolean> {
    // Find job by imageId
    for (const job of this.jobs.values()) {
      if (job.imageId === imageId && (job.status === 'queued' || job.status === 'processing')) {
        await this.cancelJob(job.jobId);
        return true;
      }
    }
    return false;
  }

  /**
   * Cancel segmentation task (compatibility method)
   */
  async cancelSegmentationTask(imageId: string): Promise<boolean> {
    return this.cancelTask(imageId);
  }

  /**
   * Process a job (simulated with progress updates)
   */
  private async processJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job || job.status !== 'queued') {
      return;
    }

    job.status = 'processing';
    job.progress = 1;
    job.stage = 'Initializing processing';
    job.startedAt = new Date();
    job.updatedAt = new Date();

    logger.info(`Starting job ${jobId} for image ${job.imageId}`);

    // Emit processing started event
    if (job.projectId) {
      broadcastSegmentationProgress(job.projectId, job.imageId, {
        status: 'processing',
        progress: 1,
        stage: 'Initializing processing',
        startedAt: job.startedAt.toISOString(),
        estimatedTimeRemaining: 15000, // 15 seconds estimated
      });
    }

    // Simulate processing stages with progress updates
    const stages = [
      { progress: 10, stage: 'Loading image', duration: 500 },
      { progress: 25, stage: 'Preprocessing image', duration: 1000 },
      { progress: 45, stage: 'Running ML model', duration: 3000 },
      { progress: 70, stage: 'Processing results', duration: 1500 },
      { progress: 85, stage: 'Extracting polygons', duration: 1000 },
      { progress: 95, stage: 'Finalizing results', duration: 500 },
    ];

    try {
      for (const { progress, stage, duration } of stages) {
        // Check if job was cancelled
        const currentJob = this.jobs.get(jobId);
        if (!currentJob || currentJob.status === 'cancelled') {
          return;
        }

        job.progress = progress;
        job.stage = stage;
        job.updatedAt = new Date();

        // Emit progress update
        if (job.projectId) {
          const remainingStages = stages.slice(stages.indexOf(stages.find(s => s.progress === progress)!) + 1);
          const estimatedTimeRemaining = remainingStages.reduce((sum, s) => sum + s.duration, 0);
          
          broadcastSegmentationProgress(job.projectId, job.imageId, {
            status: 'processing',
            progress,
            stage,
            startedAt: job.startedAt!.toISOString(),
            estimatedTimeRemaining,
          });
        }

        // Wait for stage duration
        await new Promise((resolve) => setTimeout(resolve, duration));
      }

      // Check if job was cancelled during processing
      const currentJob = this.jobs.get(jobId);
      if (!currentJob || currentJob.status === 'cancelled') {
        return;
      }

      // Simulate success/failure (90% success rate)
      if (Math.random() < 0.9) {
        job.status = 'completed';
        job.progress = 100;
        job.stage = 'Processing complete';
        job.completedAt = new Date();
        
        if (job.projectId) {
          broadcastSegmentationProgress(job.projectId, job.imageId, {
            status: 'completed',
            progress: 100,
            stage: 'Processing complete',
            startedAt: job.startedAt!.toISOString(),
          });
        }
      } else {
        job.status = 'failed';
        job.stage = 'Processing failed';
        job.completedAt = new Date();
        
        if (job.projectId) {
          broadcastSegmentationProgress(job.projectId, job.imageId, {
            status: 'failed',
            progress: job.progress,
            stage: 'Processing failed',
            startedAt: job.startedAt!.toISOString(),
            error: 'Simulated processing failure',
          });
        }
      }
      
      job.updatedAt = new Date();
      logger.info(`Job ${jobId} ${job.status}`);
      
    } catch (error) {
      logger.error(`Error processing job ${jobId}:`, error);
      job.status = 'failed';
      job.stage = 'Processing error';
      job.completedAt = new Date();
      job.updatedAt = new Date();
      
      if (job.projectId) {
        broadcastSegmentationProgress(job.projectId, job.imageId, {
          status: 'failed',
          progress: job.progress,
          stage: 'Processing error',
          startedAt: job.startedAt!.toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
  }
}

// Export singleton instance for compatibility
const segmentationQueueService = new SegmentationQueueService();

export default segmentationQueueService;
export { SegmentationQueueService };

// Named exports for controller compatibility
export const queueSegmentationJob =
  segmentationQueueService.queueSegmentationJob.bind(segmentationQueueService);
export const getJobStatus = segmentationQueueService.getJobStatus.bind(segmentationQueueService);
export const cancelJob = segmentationQueueService.cancelJob.bind(segmentationQueueService);
