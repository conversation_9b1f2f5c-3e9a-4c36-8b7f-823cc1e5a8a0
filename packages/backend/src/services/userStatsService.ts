import { prisma } from './PrismaService';
import logger from '../utils/logger';
import { cacheService, CACHE_TTL } from './cacheService';

/**
 * Interface for user statistics
 */
interface UserStats {
  totalProjects: number;
  totalImages: number;
  completedSegmentations: number;
  storageUsedBytes: bigint;
  storageLimitBytes: bigint;
  recentActivity: Array<{
    id?: string;
    type: string;
    description?: string;
    timestamp: Date | string;
    project_id?: string;
    project_name?: string;
    image_id?: string;
    image_name?: string;
    item_id?: string;
    item_name?: string;
  }>;
  recentProjects: Array<{
    id: string;
    title: string;
    description?: string;
    created_at: Date;
    updated_at: Date;
    image_count: number;
    completed_count: number;
  }>;
  recentImages: Array<{
    id: string;
    name: string;
    storage_path?: string;
    status: string;
    created_at: Date;
    updated_at: Date;
    project_id: string;
    project_name: string;
  }>;
  projectsThisMonth: number;
  projectsLastMonth: number;
  imagesThisMonth: number;
  imagesLastMonth: number;
}

/**
 * Optimized Service for handling user statistics using Prisma ORM
 */
export class UserStatsService {
  /**
   * Get comprehensive user statistics with minimal database queries
   * @param userId User ID
   */
  async getUserStats(userId: string): Promise<UserStats> {
    const startTime = Date.now();

    try {
      // Try to get from cache first
      const cacheKey = cacheService.generateKey('user:stats:', userId);
      const cached = await cacheService.get<UserStats>(cacheKey);

      if (cached) {
        logger.debug('User stats retrieved from cache', { userId });
        return cached;
      }

      logger.debug('Fetching optimized user stats from database', { userId });

      // Get date ranges for monthly stats
      const currentMonthStart = new Date();
      currentMonthStart.setDate(1);
      currentMonthStart.setHours(0, 0, 0, 0);

      const lastMonthStart = new Date();
      lastMonthStart.setMonth(lastMonthStart.getMonth() - 1);
      lastMonthStart.setDate(1);
      lastMonthStart.setHours(0, 0, 0, 0);

      const lastMonthEnd = new Date(currentMonthStart);
      lastMonthEnd.setMilliseconds(-1);

      // Fetch user projects with image counts
      const projects = await prisma.project.findMany({
        where: { userId: userId },
        include: {
          images: {
            select: {
              id: true,
              segmentationStatus: true,
              fileSize: true,
              createdAt: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      });

      // Calculate totals
      let totalImages = 0;
      let completedSegmentations = 0;
      let storageUsedBytes = BigInt(0);
      let imagesThisMonth = 0;
      let imagesLastMonth = 0;

      for (const project of projects) {
        totalImages += project.images.length;
        
        for (const image of project.images) {
          if (image.segmentationStatus === 'completed') {
            completedSegmentations++;
          }
          
          if (image.fileSize) {
            storageUsedBytes += BigInt(image.fileSize);
          }
          
          if (image.createdAt >= currentMonthStart) {
            imagesThisMonth++;
          } else if (image.createdAt >= lastMonthStart && image.createdAt <= lastMonthEnd) {
            imagesLastMonth++;
          }
        }
      }

      // Calculate project monthly stats
      const projectsThisMonth = projects.filter(p => p.createdAt >= currentMonthStart).length;
      const projectsLastMonth = projects.filter(p => 
        p.createdAt >= lastMonthStart && p.createdAt <= lastMonthEnd
      ).length;

      // Get storage limit from user profile or use default
      let storageLimitBytes = BigInt(10 * 1024 * 1024 * 1024); // 10GB default
      try {
        // Storage limit could be stored in user profile preferences
        const profile = await prisma.userProfile.findUnique({
          where: { userId },
          select: { notificationPreferences: true },
        });
        // Check if storage limit is in preferences
        const prefs = profile?.notificationPreferences as any;
        if (prefs?.storageLimitBytes) {
          storageLimitBytes = BigInt(prefs.storageLimitBytes);
        }
      } catch (error) {
        logger.debug('Storage limit not available, using default');
      }

      // Get recent projects (top 5)
      const recentProjects = projects.slice(0, 5).map(project => ({
        id: project.id,
        title: project.title,
        description: project.description || undefined,
        created_at: project.createdAt,
        updated_at: project.updatedAt,
        image_count: project.images.length,
        completed_count: project.images.filter(img => img.segmentationStatus === 'completed').length,
      }));

      // Get recent images (top 5)
      const recentImages = await prisma.image.findMany({
        where: {
          project: { userId: userId },
        },
        include: {
          project: {
            select: { title: true },
          },
        },
        orderBy: { updatedAt: 'desc' },
        take: 5,
      });

      const recentImagesFormatted = recentImages
        .filter((image) => image.project && image.name && image.projectId)
        .map(image => ({
          id: image.id,
          name: image.name!,
          storage_path: image.storagePath || undefined,
          status: image.status,
          created_at: image.createdAt,
          updated_at: image.updatedAt,
          project_id: image.projectId!,
          project_name: image.project!.title,
        }));

      // Generate recent activity
      const recentActivity = await this.generateRecentActivity(userId);

      // Prepare the final stats object
      const finalStats: UserStats = {
        totalProjects: projects.length,
        totalImages,
        completedSegmentations,
        storageUsedBytes,
        storageLimitBytes,
        recentActivity,
        recentProjects,
        recentImages: recentImagesFormatted,
        projectsThisMonth,
        projectsLastMonth,
        imagesThisMonth,
        imagesLastMonth,
      };

      const queryTime = Date.now() - startTime;
      logger.info(
        { message: 'Optimized user stats fetched successfully' },
        {
          userId,
          queryTime,
          projects: finalStats.totalProjects,
          images: finalStats.totalImages,
        }
      );

      // Cache the result
      await cacheService.set(cacheKey, finalStats, CACHE_TTL.MEDIUM);

      return finalStats;
    } catch (error) {
      logger.error('Error fetching optimized user stats', { userId, error });

      // Return default stats on error
      return {
        totalProjects: 0,
        totalImages: 0,
        completedSegmentations: 0,
        storageUsedBytes: BigInt(0),
        storageLimitBytes: BigInt(10 * 1024 * 1024 * 1024), // 10GB default
        recentActivity: [],
        recentProjects: [],
        recentImages: [],
        projectsThisMonth: 0,
        projectsLastMonth: 0,
        imagesThisMonth: 0,
        imagesLastMonth: 0,
      };
    }
  }

  /**
   * Get basic user stats with a single query (for frequent polling)
   */
  async getBasicStats(userId: string) {
    // Use cache wrapper for basic stats
    const cacheKey = cacheService.generateKey('user:stats:basic:', userId);

    return cacheService.cached(
      cacheKey,
      async () => {
        const projects = await prisma.project.findMany({
          where: { userId: userId },
          include: {
            images: {
              select: {
                id: true,
                segmentationStatus: true,
                fileSize: true,
              },
            },
          },
        });

        let totalImages = 0;
        let completedSegmentations = 0;
        let storageUsedBytes = BigInt(0);

        for (const project of projects) {
          totalImages += project.images.length;
          
          for (const image of project.images) {
            if (image.segmentationStatus === 'completed') {
              completedSegmentations++;
            }
            
            if (image.fileSize) {
              storageUsedBytes += BigInt(image.fileSize);
            }
          }
        }

        return {
          totalProjects: projects.length,
          totalImages,
          completedSegmentations,
          storageUsedBytes,
        };
      },
      CACHE_TTL.SHORT
    ); // 5 minutes for basic stats
  }

  /**
   * Invalidate user stats cache
   */
  async invalidateUserStatsCache(userId: string) {
    await cacheService.invalidateRelated('user', userId);
  }

  /**
   * Generate recent activity from projects and images
   */
  async generateRecentActivity(userId: string) {
    try {
      // Get recent project activities
      const recentProjects = await prisma.project.findMany({
        where: {
          userId: userId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      // Get recent image activities
      const recentImages = await prisma.image.findMany({
        where: {
          project: { userId: userId },
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        include: {
          project: {
            select: { id: true, title: true },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      // Get recent segmentation completions
      const recentCompletions = await prisma.image.findMany({
        where: {
          project: { userId: userId },
          segmentationStatus: 'completed',
          updatedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        include: {
          project: {
            select: { id: true, title: true },
          },
        },
        orderBy: { updatedAt: 'desc' },
        take: 10,
      });

      const activities = [
        ...recentProjects.map((project, index) => ({
          id: `p${index}`,
          type: 'project_created' as const,
          description: `Created project "${project.title}"`,
          timestamp: project.createdAt.toISOString(),
          project_id: project.id,
          project_name: project.title,
          item_id: project.id,
          item_name: project.title,
        })),
        ...recentImages.map((image, index) => ({
          id: `i${index}`,
          type: 'image_uploaded' as const,
          description: `Uploaded image "${image.name || 'Unnamed'}"`,
          timestamp: image.createdAt.toISOString(),
          project_id: image.projectId || undefined,
          project_name: image.project?.title || 'Unknown Project',
          image_id: image.id,
          image_name: image.name || undefined,
          item_id: image.id,
          item_name: image.name || undefined,
        })),
        ...recentCompletions.map((image, index) => ({
          id: `c${index}`,
          type: 'segmentation_completed' as const,
          description: `Completed segmentation for "${image.name || 'Unnamed'}"`,
          timestamp: image.updatedAt.toISOString(),
          project_id: image.projectId || undefined,
          project_name: image.project?.title || 'Unknown Project',
          image_id: image.id,
          image_name: image.name || undefined,
          item_id: image.id,
          item_name: image.name || undefined,
        })),
      ];

      // Sort by timestamp and take the most recent 20
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 20);
    } catch (error) {
      logger.error('Error generating recent activity', { userId, error });
      return [];
    }
  }
}

// Export singleton instance
export default new UserStatsService();