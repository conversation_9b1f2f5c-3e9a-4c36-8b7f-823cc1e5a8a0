/**
 * Database Optimization Service
 *
 * Comprehensive database optimization using Prisma ORM,
 * intelligent caching, connection pooling, and performance monitoring
 */

import { prisma } from './PrismaService';
import logger from '../utils/logger';
import { cacheService } from './cacheService';

interface OptimizationConfig {
  enableQueryCache: boolean;
  enablePreparedStatements: boolean;
  enableConnectionPooling: boolean;
  maxConnections: number;
  queryTimeout: number;
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative';
  monitoringEnabled: boolean;
}

interface PerformanceProfile {
  averageQueryTime: number;
  slowQueryCount: number;
  cacheHitRate: number;
  connectionPoolUtilization: number;
  recommendedOptimizations: string[];
}

interface DatabaseHealth {
  connectionPool: {
    total: number;
    idle: number;
    waiting: number;
  };
  performance: {
    averageQueryTime: number;
    slowQueries: number;
    totalQueries: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    size: number;
  };
  recommendations: string[];
}

interface UserStats {
  total_projects: number;
  total_images: number;
  completed_segmentations: number;
  processing_images: number;
  queued_images: number;
  storage_used_bytes: number;
  projects_this_month: number;
  projects_last_month: number;
  images_this_month: number;
  images_last_month: number;
  recentProjects: unknown[];
  recentActivity: unknown[];
  lastUpdated: string;
}

interface ProjectListResult {
  projects: unknown[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface ImageListResult {
  images: unknown[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface FilterOptions {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

interface OptimizationMetrics {
  query: {
    averageTime: number;
    slowQueries: number;
    totalQueries: number;
    topQueries?: Array<{
      query: string;
      avgTime: number;
    }>;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    memoryCacheSize: number;
  };
  config: OptimizationConfig;
}

class DatabaseOptimizationService {
  private config: OptimizationConfig;
  private healthChecks: DatabaseHealth[] = [];
  private monitoringInterval!: NodeJS.Timeout;
  private queryMetrics = {
    totalQueries: 0,
    averageTime: 0,
    slowQueries: 0,
    queryTimes: [] as number[],
  };

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = {
      enableQueryCache: true,
      enablePreparedStatements: true,
      enableConnectionPooling: true,
      maxConnections: 20,
      queryTimeout: 30000,
      cacheStrategy: 'moderate',
      monitoringEnabled: true,
      ...config,
    };

    if (this.config.monitoringEnabled) {
      this.startHealthMonitoring();
    }
  }

  /**
   * Optimized user statistics query with multi-layer caching
   */
  async getUserStatsOptimized(userId: string): Promise<UserStats | null> {
    const cacheKey = `user_stats_v2:${userId}`;
    const startTime = Date.now();

    try {
      // Try to get from cache first
      const cached = await cacheService.get<UserStats>(cacheKey);
      if (cached) {
        return cached;
      }

      // Get date ranges
      const currentMonthStart = new Date();
      currentMonthStart.setDate(1);
      currentMonthStart.setHours(0, 0, 0, 0);

      const lastMonthStart = new Date();
      lastMonthStart.setMonth(lastMonthStart.getMonth() - 1);
      lastMonthStart.setDate(1);
      lastMonthStart.setHours(0, 0, 0, 0);

      const lastMonthEnd = new Date(currentMonthStart);
      lastMonthEnd.setMilliseconds(-1);

      // Get user projects with aggregated data
      const projects = await prisma.project.findMany({
        where: { userId: userId },
        include: {
          images: {
            select: {
              id: true,
              segmentationStatus: true,
              fileSize: true,
              createdAt: true,
              updatedAt: true,
              name: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      });

      // Calculate statistics
      let total_images = 0;
      let completed_segmentations = 0;
      let processing_images = 0;
      let queued_images = 0;
      let storage_used_bytes = 0;
      let images_this_month = 0;
      let images_last_month = 0;

      const recentProjects = [];
      const recentActivity = [];

      for (const project of projects) {
        total_images += project.images.length;

        // Calculate image statistics
        for (const image of project.images) {
          switch (image.segmentationStatus) {
            case 'completed':
              completed_segmentations++;
              break;
            case 'processing':
              processing_images++;
              break;
            case 'queued':
              queued_images++;
              break;
          }

          if (image.fileSize) {
            storage_used_bytes += Number(image.fileSize);
          }

          if (image.createdAt >= currentMonthStart) {
            images_this_month++;
          } else if (image.createdAt >= lastMonthStart && image.createdAt <= lastMonthEnd) {
            images_last_month++;
          }

          // Add to recent activity if within 30 days
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          if (image.createdAt >= thirtyDaysAgo) {
            recentActivity.push({
              type: 'image_uploaded',
              item_name: image.name,
              item_id: image.id,
              timestamp: image.createdAt,
              project_name: project.title,
              project_id: project.id,
            });
          }

          if (image.segmentationStatus === 'completed' && image.updatedAt >= thirtyDaysAgo) {
            recentActivity.push({
              type: 'segmentation_completed',
              item_name: image.name,
              item_id: image.id,
              timestamp: image.updatedAt,
              project_name: project.title,
              project_id: project.id,
            });
          }
        }

        // Add project to recent projects (top 5)
        if (recentProjects.length < 5) {
          recentProjects.push({
            id: project.id,
            title: project.title,
            description: project.description,
            created_at: project.createdAt,
            updated_at: project.updatedAt,
            image_count: project.images.length,
            completed_count: project.images.filter((img) => img.segmentationStatus === 'completed')
              .length,
            total_size: project.images.reduce((sum, img) => sum + (Number(img.fileSize) || 0), 0),
          });
        }

        // Add project creation to recent activity if within 30 days
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        if (project.createdAt >= thirtyDaysAgo) {
          recentActivity.push({
            type: 'project_created',
            item_name: project.title,
            item_id: project.id,
            timestamp: project.createdAt,
            project_name: project.title,
            project_id: project.id,
          });
        }
      }

      // Calculate project monthly statistics
      const projects_this_month = projects.filter((p) => p.createdAt >= currentMonthStart).length;
      const projects_last_month = projects.filter(
        (p) => p.createdAt >= lastMonthStart && p.createdAt <= lastMonthEnd
      ).length;

      // Sort recent activity by timestamp
      recentActivity.sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      const userStats: UserStats = {
        total_projects: projects.length,
        total_images,
        completed_segmentations,
        processing_images,
        queued_images,
        storage_used_bytes,
        projects_this_month,
        projects_last_month,
        images_this_month,
        images_last_month,
        recentProjects,
        recentActivity: recentActivity.slice(0, 20),
        lastUpdated: new Date().toISOString(),
      };

      // Cache the result
      await cacheService.set(cacheKey, userStats, 5 * 60 * 1000); // 5 minutes

      // Update metrics
      this.updateQueryMetrics(startTime);

      return userStats;
    } catch (error) {
      logger.error('Error fetching optimized user stats', { userId, error });
      this.queryMetrics.slowQueries++;
      throw error;
    }
  }

  /**
   * Optimized project list query with intelligent pagination
   */
  async getProjectListOptimized(
    userId: string,
    page: number = 1,
    limit: number = 20,
    filters: FilterOptions = {}
  ): Promise<ProjectListResult> {
    const cacheKey = `project_list:${userId}:${page}:${limit}:${JSON.stringify(filters)}`;
    const startTime = Date.now();

    try {
      const cached = await cacheService.get<ProjectListResult>(cacheKey);
      if (cached) {
        return cached;
      }

      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = { userId: userId };

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.dateFrom) {
        where.createdAt = { gte: new Date(filters.dateFrom) };
      }

      if (filters.dateTo) {
        if (where.createdAt) {
          where.createdAt.lte = new Date(filters.dateTo);
        } else {
          where.createdAt = { lte: new Date(filters.dateTo) };
        }
      }

      // Get projects with counts
      const [projects, total] = await Promise.all([
        prisma.project.findMany({
          where,
          include: {
            images: {
              select: {
                id: true,
                segmentationStatus: true,
                fileSize: true,
                updatedAt: true,
              },
            },
          },
          orderBy: { updatedAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.project.count({ where }),
      ]);

      // Format projects with statistics
      const formattedProjects = projects.map((project) => ({
        ...project,
        image_count: project.images.length,
        completed_count: project.images.filter((img) => img.segmentationStatus === 'completed')
          .length,
        processing_count: project.images.filter((img) => img.segmentationStatus === 'processing')
          .length,
        queued_count: project.images.filter((img) => img.segmentationStatus === 'queued').length,
        total_size: project.images.reduce((sum, img) => sum + (Number(img.fileSize) || 0), 0),
        last_activity:
          project.images.length > 0
            ? Math.max(...project.images.map((img) => img.updatedAt.getTime()))
            : project.updatedAt.getTime(),
      }));

      const result: ProjectListResult = {
        projects: formattedProjects,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };

      // Cache the result
      await cacheService.set(cacheKey, result, 3 * 60 * 1000); // 3 minutes

      // Update metrics
      this.updateQueryMetrics(startTime);

      return result;
    } catch (error) {
      logger.error('Error fetching optimized project list', { userId, page, limit, error });
      this.queryMetrics.slowQueries++;
      throw error;
    }
  }

  /**
   * Optimized image list query with lazy loading support
   */
  async getImageListOptimized(
    projectId: string,
    page: number = 1,
    limit: number = 50,
    includeThumbnails: boolean = false
  ): Promise<ImageListResult> {
    const cacheKey = `image_list:${projectId}:${page}:${limit}:${includeThumbnails}`;
    const startTime = Date.now();

    try {
      const cached = await cacheService.get<ImageListResult>(cacheKey);
      if (cached) {
        return cached;
      }

      const offset = (page - 1) * limit;

      const [images, total] = await Promise.all([
        prisma.image.findMany({
          where: { projectId: projectId },
          include: {
            segmentations: {
              select: {
                id: true,
                status: true,
                createdAt: true,
              },
              take: 1,
              orderBy: { createdAt: 'desc' },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.image.count({ where: { projectId: projectId } }),
      ]);

      const formattedImages = images.map((image) => ({
        ...image,
        segmentation_result: image.segmentations.length > 0 ? image.segmentations[0] : null,
      }));

      const result: ImageListResult = {
        images: formattedImages,
        total,
        page,
        limit,
        hasMore: offset + limit < total,
      };

      // Cache the result
      await cacheService.set(cacheKey, result, 2 * 60 * 1000); // 2 minutes

      // Update metrics
      this.updateQueryMetrics(startTime);

      return result;
    } catch (error) {
      logger.error('Error fetching optimized image list', { projectId, page, limit, error });
      this.queryMetrics.slowQueries++;
      throw error;
    }
  }

  /**
   * Invalidate related caches when data changes
   */
  async invalidateRelatedCaches(type: 'user' | 'project' | 'image', id: string): Promise<void> {
    try {
      switch (type) {
        case 'user': {
          await cacheService.invalidateRelated('user', id);
          break;
        }

        case 'project': {
          await cacheService.invalidateRelated('project', id);
          // Also invalidate user caches for project owner
          const project = await prisma.project.findUnique({
            where: { id },
            select: { userId: true },
          });
          if (project) {
            await cacheService.invalidateRelated('user', project.userId);
          }
          break;
        }

        case 'image': {
          // Invalidate project and user caches
          const image = await prisma.image.findUnique({
            where: { id },
            include: {
              project: { select: { userId: true } },
            },
          });
          if (image) {
            if (image.projectId) {
              await cacheService.invalidateRelated('project', image.projectId);
            }
            if (image.project?.userId) {
              await cacheService.invalidateRelated('user', image.project.userId);
            }
          }
          break;
        }
      }
    } catch (error) {
      logger.error('Error invalidating related caches', { type, id, error });
    }
  }

  /**
   * Generate performance profile and recommendations
   */
  async generatePerformanceProfile(): Promise<PerformanceProfile> {
    const cacheMetrics = cacheService.getMetrics
      ? cacheService.getMetrics()
      : { hitRate: 0, memoryUsage: 0 };
    const recommendations: string[] = [];

    // Analyze query performance
    if (this.queryMetrics.averageTime > 1000) {
      recommendations.push(
        'Critical: Average query time exceeds 1 second - implement query optimization immediately'
      );
    } else if (this.queryMetrics.averageTime > 500) {
      recommendations.push('Warning: Consider adding database indexes for frequently used columns');
    }

    if (this.queryMetrics.slowQueries > 20) {
      recommendations.push(
        'Critical: High number of slow queries detected - review query execution plans'
      );
    } else if (this.queryMetrics.slowQueries > 10) {
      recommendations.push('Warning: Review and optimize slow queries');
    }

    // Analyze cache performance
    const hitRate = cacheMetrics.hitRate || 0;
    if (hitRate < 50) {
      recommendations.push('Critical: Very low cache hit rate - implement cache warming strategy');
      recommendations.push('Consider reviewing cache key generation logic');
    } else if (hitRate < 70) {
      recommendations.push('Increase cache TTL for stable data');
      recommendations.push('Consider warming up frequently accessed data');
    }

    const memoryUsage = cacheMetrics.memoryUsage || 0;
    if (memoryUsage > 200 * 1024 * 1024) {
      // 200MB
      recommendations.push(
        'Critical: High memory usage - implement immediate cache eviction strategy'
      );
    } else if (memoryUsage > 100 * 1024 * 1024) {
      // 100MB
      recommendations.push('Consider reducing memory cache size or implementing LRU eviction');
    }

    return {
      averageQueryTime: this.queryMetrics.averageTime,
      slowQueryCount: this.queryMetrics.slowQueries,
      cacheHitRate: hitRate,
      connectionPoolUtilization: 0, // Prisma manages this internally
      recommendedOptimizations: recommendations,
    };
  }

  /**
   * Update query metrics
   */
  private updateQueryMetrics(startTime: number): void {
    const duration = Date.now() - startTime;
    this.queryMetrics.totalQueries++;
    this.queryMetrics.queryTimes.push(duration);

    // Keep only last 1000 queries
    if (this.queryMetrics.queryTimes.length > 1000) {
      this.queryMetrics.queryTimes.shift();
    }

    // Calculate average
    this.queryMetrics.averageTime =
      this.queryMetrics.queryTimes.reduce((a, b) => a + b, 0) / this.queryMetrics.queryTimes.length;

    // Count slow queries
    if (duration > 1000) {
      this.queryMetrics.slowQueries++;
      logger.warn('Slow query detected', { duration });
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        const health = await this.checkDatabaseHealth();
        this.healthChecks.push(health);

        // Keep only last 24 hours of health checks (assuming 5-minute intervals)
        if (this.healthChecks.length > 288) {
          this.healthChecks.shift();
        }

        // Log critical issues
        if (health.recommendations.length > 0) {
          logger.warn('Database optimization recommendations', {
            recommendations: health.recommendations,
          });
        }
      } catch (error) {
        logger.error('Health monitoring error', { error });
      }
    }, 300000); // Every 5 minutes
  }

  /**
   * Check database health
   */
  async checkDatabaseHealth(): Promise<DatabaseHealth> {
    const cacheMetrics = cacheService.getMetrics
      ? cacheService.getMetrics()
      : {
          hitRate: 0,
          memoryUsage: 0,
          size: 0,
        };

    const recommendations: string[] = [];

    // Check performance health
    if (this.queryMetrics.averageTime > 1000) {
      recommendations.push('Average query time is too high');
    }

    if ((cacheMetrics.hitRate ?? 0) < 50) {
      recommendations.push('Cache hit rate is below optimal threshold');
    }

    return {
      connectionPool: {
        total: 0, // Prisma manages this
        idle: 0,
        waiting: 0,
      },
      performance: {
        averageQueryTime: this.queryMetrics.averageTime,
        slowQueries: this.queryMetrics.slowQueries,
        totalQueries: this.queryMetrics.totalQueries,
      },
      cache: {
        hitRate: cacheMetrics.hitRate ?? 0,
        memoryUsage: cacheMetrics.memoryUsage ?? 0,
        size: cacheMetrics.size ?? 0,
      },
      recommendations,
    };
  }

  /**
   * Get current metrics
   */
  getMetrics(): OptimizationMetrics {
    const cacheMetrics = cacheService.getMetrics
      ? cacheService.getMetrics()
      : {
          hitRate: 0,
          memoryUsage: 0,
          memoryCacheSize: 0,
        };

    return {
      query: {
        averageTime: this.queryMetrics.averageTime,
        slowQueries: this.queryMetrics.slowQueries,
        totalQueries: this.queryMetrics.totalQueries,
      },
      cache: {
        hitRate: cacheMetrics.hitRate ?? 0,
        memoryUsage: cacheMetrics.memoryUsage ?? 0,
        memoryCacheSize: cacheMetrics.memoryCacheSize ?? 0,
      },
      config: this.config,
    };
  }

  /**
   * Get health history
   */
  getHealthHistory(): DatabaseHealth[] {
    return [...this.healthChecks];
  }

  /**
   * Clear all caches
   */
  async clearAllCaches(): Promise<void> {
    await cacheService.clear();
  }

  /**
   * Shutdown optimization service
   */
  async shutdown(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
  }
}

export default DatabaseOptimizationService;
