/**
 * Prometheus monitoring integration for production
 * 
 * ⚠️ LEGACY FILE - DEPRECATED ⚠️
 * This file has been superseded by the unified monitoring system.
 * 
 * New implementation: Use UnifiedMonitoringRegistry.ts and shared/src/monitoring/
 * Migration: This file will be removed after all references are updated.
 * 
 * @deprecated Use unified monitoring system instead
 */

import { Application, Request, Response, NextFunction } from 'express';
import promClient from 'prom-client';
import logger from '../utils/logger';

// Create a Registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({
  register,
  prefix: 'spheroseg_',
});

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'spheroseg_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
  registers: [register],
});

const httpRequestsTotal = new promClient.Counter({
  name: 'spheroseg_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
  registers: [register],
});

const activeConnections = new promClient.Gauge({
  name: 'spheroseg_active_connections',
  help: 'Number of active connections',
  registers: [register],
});

const databaseConnectionsActive = new promClient.Gauge({
  name: 'spheroseg_database_connections_active',
  help: 'Number of active database connections',
  registers: [register],
});

const databaseConnectionsIdle = new promClient.Gauge({
  name: 'spheroseg_database_connections_idle',
  help: 'Number of idle database connections',
  registers: [register],
});

const uploadSize = new promClient.Histogram({
  name: 'spheroseg_upload_size_bytes',
  help: 'Size of uploaded files in bytes',
  labelNames: ['type'],
  buckets: [1024, 10240, 102400, 1048576, 10485760, 104857600], // 1KB to 100MB
  registers: [register],
});

const uploadDuration = new promClient.Histogram({
  name: 'spheroseg_upload_duration_seconds',
  help: 'Duration of file uploads in seconds',
  labelNames: ['type', 'success'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30],
  registers: [register],
});

const mlProcessingDuration = new promClient.Histogram({
  name: 'spheroseg_ml_processing_duration_seconds',
  help: 'Duration of ML processing in seconds',
  labelNames: ['model', 'success'],
  buckets: [1, 5, 10, 30, 60, 120, 300],
  registers: [register],
});

const errorsByType = new promClient.Counter({
  name: 'spheroseg_errors_total',
  help: 'Total number of errors by type',
  labelNames: ['type', 'code'],
  registers: [register],
});

const userSessions = new promClient.Gauge({
  name: 'spheroseg_user_sessions_active',
  help: 'Number of active user sessions',
  registers: [register],
});

const projectsCreated = new promClient.Counter({
  name: 'spheroseg_projects_created_total',
  help: 'Total number of projects created',
  registers: [register],
});

const imagesProcessed = new promClient.Counter({
  name: 'spheroseg_images_processed_total',
  help: 'Total number of images processed',
  labelNames: ['success'],
  registers: [register],
});

/**
 * Middleware to collect HTTP metrics
 */
export function metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();

  // Track active connections
  activeConnections.inc();

  // Clean up the route for consistent labeling
  const route = req.route?.path || req.path;
  const cleanRoute = route.replace(/\/:\w+/g, '/:id'); // Replace :param with :id

  // Override res.end to capture metrics
  const originalEnd = res.end;
  res.end = function (this: Response, chunk?: any, encoding?: BufferEncoding, cb?: () => void) {
    const duration = (Date.now() - startTime) / 1000;
    const statusCode = res.statusCode.toString();

    // Record metrics
    httpRequestDuration.labels(req.method, cleanRoute, statusCode).observe(duration);

    httpRequestsTotal.labels(req.method, cleanRoute, statusCode).inc();

    // Track errors
    if (res.statusCode >= 400) {
      const errorType = res.statusCode >= 500 ? 'server_error' : 'client_error';
      errorsByType.labels(errorType, statusCode).inc();
    }

    // Decrease active connections
    activeConnections.dec();

    // Call original end method
    if (encoding && cb) {
      return originalEnd.call(this, chunk, encoding, cb);
    } else if (encoding) {
      return originalEnd.call(this, chunk, encoding);
    } else if (chunk) {
      return originalEnd.call(this, chunk);
    } else {
      return originalEnd.call(this);
    }
  } as any;

  next();
}

/**
 * Track upload metrics
 */
export function trackUpload(
  type: string,
  sizeBytes: number,
  durationSeconds: number,
  success: boolean
): void {
  uploadSize.labels(type).observe(sizeBytes);
  uploadDuration.labels(type, success.toString()).observe(durationSeconds);
}

/**
 * Track ML processing metrics
 */
export function trackMLProcessing(model: string, durationSeconds: number, success: boolean): void {
  mlProcessingDuration.labels(model, success.toString()).observe(durationSeconds);
}

/**
 * Track database pool metrics
 */
export function updateDatabaseMetrics(activeCount: number, idleCount: number): void {
  databaseConnectionsActive.set(activeCount);
  databaseConnectionsIdle.set(idleCount);
}

/**
 * Track user session metrics
 */
export function updateUserSessions(count: number): void {
  userSessions.set(count);
}

/**
 * Track project creation
 */
export function trackProjectCreated(): void {
  projectsCreated.inc();
}

/**
 * Track image processing
 */
export function trackImageProcessed(success: boolean): void {
  imagesProcessed.labels(success.toString()).inc();
}

/**
 * Track custom errors
 */
export function trackError(type: string, code?: string): void {
  errorsByType.labels(type, code || 'unknown').inc();
}

/**
 * Initialize monitoring and add metrics endpoint
 */
export async function initializeMonitoring(app: Application): Promise<void> {
  try {
    // Add metrics middleware to all routes
    app.use(metricsMiddleware);

    // Metrics endpoint
    app.get('/metrics', async (req: Request, res: Response) => {
      try {
        res.set('Content-Type', register.contentType);
        const metrics = await register.metrics();
        res.end(metrics);
      } catch (error) {
        logger.error('Error generating metrics:', {
          error: error instanceof Error ? error.message : String(error),
        });
        res.status(500).json({ error: 'Failed to generate metrics' });
      }
    });

    // Health check with metrics
    app.get('/health/metrics', async (req: Request, res: Response) => {
      try {
        const metrics = await register.getSingleMetricAsString('spheroseg_http_requests_total');
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          metrics: {
            available: true,
            sample: metrics ? 'Available' : 'No data',
          },
        });
      } catch (error) {
        logger.error('Health check metrics error:', {
          error: error instanceof Error ? error.message : String(error),
        });
        res.status(500).json({
          status: 'unhealthy',
          error: 'Metrics collection failed',
        });
      }
    });

    logger.info({ message: '✅ Prometheus monitoring initialized' });
    logger.info({ message: '📊 Metrics endpoint: /metrics' });
  } catch (error) {
    logger.error('❌ Failed to initialize monitoring:', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Get current metrics summary for logging
 */
export async function getMetricsSummary(): Promise<any> {
  try {
    const metrics = await register.metrics();
    const lines = metrics.split('\n').filter((line) => !line.startsWith('#') && line.trim());

    const summary: any = {};
    lines.forEach((line) => {
      const [name] = line.split(' ');
      if (name) {
        summary[name] = 'recorded';
      }
    });

    return summary;
  } catch (error) {
    logger.error('Error getting metrics summary:', {
      error: error instanceof Error ? error.message : String(error),
    });
    return { error: 'Failed to get metrics' };
  }
}

/**
 * Custom metric collection for specific business logic
 */
export const customMetrics = {
  // Track specific user actions
  trackUserAction: (action: string) => {
    const userActions = new promClient.Counter({
      name: 'spheroseg_user_actions_total',
      help: 'Total number of user actions',
      labelNames: ['action'],
      registers: [register],
    });
    userActions.labels(action).inc();
  },

  // Track API response times by endpoint
  trackAPIResponse: (endpoint: string, method: string, duration: number, success: boolean) => {
    const apiResponseTime = new promClient.Histogram({
      name: 'spheroseg_api_response_time_seconds',
      help: 'API response time in seconds',
      labelNames: ['endpoint', 'method', 'success'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [register],
    });
    apiResponseTime.labels(endpoint, method, success.toString()).observe(duration);
  },

  // Track memory usage patterns
  trackMemoryUsage: () => {
    const memoryUsage = process.memoryUsage();
    const heapUsed = new promClient.Gauge({
      name: 'spheroseg_memory_heap_used_bytes',
      help: 'Used heap memory in bytes',
      registers: [register],
    });
    const heapTotal = new promClient.Gauge({
      name: 'spheroseg_memory_heap_total_bytes',
      help: 'Total heap memory in bytes',
      registers: [register],
    });

    heapUsed.set(memoryUsage.heapUsed);
    heapTotal.set(memoryUsage.heapTotal);
  },
};

// Export the registry for external use
export { register };

// Metrics endpoint handler
export const metricsEndpoint = async (req: Request, res: Response) => {
  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    logger.error('Error generating metrics:', {
      error: error instanceof Error ? error.message : String(error),
    });
    res.status(500).json({ error: 'Failed to generate metrics' });
  }
};

// Add missing exports for compatibility
export const metricsHandler = metricsEndpoint;
export const prometheusMiddleware = metricsMiddleware;
