/**
 * Unified Monitoring Registry
 * Single source of truth for all monitoring to prevent duplicate collectors
 * Prevents memory leaks and reduces overhead from multiple monitoring systems
 */

import { Registry, Gauge, Counter, Histogram } from 'prom-client';
import { logger } from '../utils/logger';

interface CollectorInfo {
  id: string;
  interval: number;
  timer: NodeJS.Timer;
  collector: () => Promise<void> | void;
  lastRun?: Date;
  errorCount: number;
}

interface MetricsCacheEntry {
  value: any;
  timestamp: Date;
  ttl: number;
}

export class UnifiedMonitoringRegistry {
  private static instance: UnifiedMonitoringRegistry;
  private collectors = new Map<string, CollectorInfo>();
  private metricsCache = new Map<string, MetricsCacheEntry>();
  private prometheusRegistry: Registry;
  private shutdownHandlers: (() => void)[] = [];
  private isShuttingDown = false;
  
  // Meta-metrics to monitor the monitors
  private collectorErrors: Counter;
  private collectorDuration: Histogram;
  private activeCollectors: Gauge;
  private cacheSize: Gauge;
  
  private constructor() {
    this.prometheusRegistry = new Registry();
    this.initializeMetaMetrics();
    this.setupShutdownHandlers();
  }
  
  static getInstance(): UnifiedMonitoringRegistry {
    if (!this.instance) {
      this.instance = new UnifiedMonitoringRegistry();
    }
    return this.instance;
  }
  
  private initializeMetaMetrics(): void {
    this.collectorErrors = new Counter({
      name: 'monitoring_collector_errors_total',
      help: 'Total number of errors in monitoring collectors',
      labelNames: ['collector_id'],
      registers: [this.prometheusRegistry]
    });
    
    this.collectorDuration = new Histogram({
      name: 'monitoring_collector_duration_seconds',
      help: 'Duration of monitoring collector execution',
      labelNames: ['collector_id'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.prometheusRegistry]
    });
    
    this.activeCollectors = new Gauge({
      name: 'monitoring_active_collectors',
      help: 'Number of active monitoring collectors',
      registers: [this.prometheusRegistry]
    });
    
    this.cacheSize = new Gauge({
      name: 'monitoring_cache_entries',
      help: 'Number of entries in metrics cache',
      registers: [this.prometheusRegistry]
    });
  }
  
  private setupShutdownHandlers(): void {
    const shutdownHandler = () => {
      if (!this.isShuttingDown) {
        this.isShuttingDown = true;
        logger.info('Shutting down monitoring registry...');
        this.cleanup();
      }
    };
    
    process.on('SIGTERM', shutdownHandler);
    process.on('SIGINT', shutdownHandler);
    process.on('beforeExit', shutdownHandler);
  }
  
  /**
   * Register a new metrics collector
   * @param id Unique identifier for the collector
   * @param interval Collection interval in milliseconds
   * @param collector Function to collect metrics
   * @returns boolean indicating success
   */
  registerCollector(
    id: string,
    interval: number,
    collector: () => Promise<void> | void
  ): boolean {
    if (this.isShuttingDown) {
      logger.warn('Cannot register collector during shutdown');
      return false;
    }
    
    // Prevent duplicate collectors
    if (this.collectors.has(id)) {
      logger.warn(`Collector ${id} already registered, skipping duplicate`);
      return false;
    }
    
    // Validate interval
    if (interval < 1000) {
      logger.warn(`Collector ${id} interval too short (${interval}ms), using 1000ms minimum`);
      interval = Math.max(interval, 1000);
    }
    
    // Wrap collector with error handling and metrics
    const wrappedCollector = async () => {
      if (this.isShuttingDown) return;
      
      const startTime = Date.now();
      const collectorInfo = this.collectors.get(id);
      
      try {
        await collector();
        if (collectorInfo) {
          collectorInfo.lastRun = new Date();
        }
        
        const duration = (Date.now() - startTime) / 1000;
        this.collectorDuration.observe({ collector_id: id }, duration);
        
        if (duration > interval * 0.8 / 1000) {
          logger.warn(`Collector ${id} took ${duration}s, close to interval ${interval}ms`);
        }
      } catch (error) {
        logger.error(`Error in collector ${id}:`, error);
        this.collectorErrors.inc({ collector_id: id });
        
        if (collectorInfo) {
          collectorInfo.errorCount++;
          
          // Disable collector after too many errors
          if (collectorInfo.errorCount > 10) {
            logger.error(`Disabling collector ${id} after 10 consecutive errors`);
            this.unregisterCollector(id);
          }
        }
      }
    };
    
    // Start the collector
    const timer = setInterval(wrappedCollector, interval);
    
    // Run immediately on registration
    wrappedCollector();
    
    this.collectors.set(id, {
      id,
      interval,
      timer,
      collector,
      lastRun: new Date(),
      errorCount: 0
    });
    
    this.activeCollectors.set(this.collectors.size);
    logger.info(`Registered collector ${id} with interval ${interval}ms`);
    
    return true;
  }
  
  /**
   * Unregister a metrics collector
   * @param id Collector identifier
   */
  unregisterCollector(id: string): boolean {
    const collectorInfo = this.collectors.get(id);
    if (!collectorInfo) {
      return false;
    }
    
    clearInterval(collectorInfo.timer);
    this.collectors.delete(id);
    this.activeCollectors.set(this.collectors.size);
    
    logger.info(`Unregistered collector ${id}`);
    return true;
  }
  
  /**
   * Update metrics cache with TTL
   * @param key Cache key
   * @param value Metric value
   * @param ttl Time to live in seconds (default 60)
   */
  updateCache(key: string, value: any, ttl: number = 60): void {
    this.metricsCache.set(key, {
      value,
      timestamp: new Date(),
      ttl: ttl * 1000 // Convert to milliseconds
    });
    
    this.cacheSize.set(this.metricsCache.size);
    
    // Clean expired entries periodically
    if (this.metricsCache.size > 1000) {
      this.cleanExpiredCache();
    }
  }
  
  /**
   * Get value from metrics cache
   * @param key Cache key
   * @returns Cached value or null if expired/missing
   */
  getCached(key: string): any | null {
    const entry = this.metricsCache.get(key);
    if (!entry) return null;
    
    const age = Date.now() - entry.timestamp.getTime();
    if (age > entry.ttl) {
      this.metricsCache.delete(key);
      return null;
    }
    
    return entry.value;
  }
  
  /**
   * Clean expired cache entries
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, entry] of this.metricsCache.entries()) {
      const age = now - entry.timestamp.getTime();
      if (age > entry.ttl) {
        this.metricsCache.delete(key);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      logger.debug(`Cleaned ${cleaned} expired cache entries`);
      this.cacheSize.set(this.metricsCache.size);
    }
  }
  
  /**
   * Get Prometheus registry
   */
  getPrometheusRegistry(): Registry {
    return this.prometheusRegistry;
  }
  
  /**
   * Get collector status
   */
  getCollectorStatus(): Array<{
    id: string;
    interval: number;
    lastRun?: Date;
    errorCount: number;
    isActive: boolean;
  }> {
    return Array.from(this.collectors.values()).map(info => ({
      id: info.id,
      interval: info.interval,
      lastRun: info.lastRun,
      errorCount: info.errorCount,
      isActive: true
    }));
  }
  
  /**
   * Add cleanup handler for graceful shutdown
   */
  addShutdownHandler(handler: () => void): void {
    this.shutdownHandlers.push(handler);
  }
  
  /**
   * Clean up all collectors and resources
   */
  cleanup(): void {
    logger.info('Cleaning up monitoring registry...');
    
    // Stop all collectors
    for (const [id, info] of this.collectors.entries()) {
      clearInterval(info.timer);
      logger.debug(`Stopped collector ${id}`);
    }
    
    // Run custom shutdown handlers
    for (const handler of this.shutdownHandlers) {
      try {
        handler();
      } catch (error) {
        logger.error('Error in shutdown handler:', error);
      }
    }
    
    // Clear all data
    this.collectors.clear();
    this.metricsCache.clear();
    this.activeCollectors.set(0);
    this.cacheSize.set(0);
    
    logger.info('Monitoring registry cleanup complete');
  }
  
  /**
   * Check if registry is shutting down
   */
  isShuttingDownStatus(): boolean {
    return this.isShuttingDown;
  }
}

// Export singleton instance
export const monitoringRegistry = UnifiedMonitoringRegistry.getInstance();