/**
 * Unified System Metrics Collector
 * Consolidates all system metrics collection into a single efficient collector
 * Replaces multiple overlapping collectors to reduce overhead
 */

import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Gauge, Registry } from 'prom-client';
import { monitoringRegistry } from '../UnifiedMonitoringRegistry';
import { logger } from '../../utils/logger';

const readFile = promisify(fs.readFile);

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  disk: {
    total?: number;
    used?: number;
    free?: number;
    percentage?: number;
  };
  network: {
    rxBytes?: number;
    txBytes?: number;
    rxPackets?: number;
    txPackets?: number;
  };
  eventLoop: {
    latency?: number;
    utilization?: number;
  };
  process: {
    uptime: number;
    pid: number;
    ppid: number;
    nodeVersion: string;
  };
}

export class UnifiedSystemCollector {
  private static instance: UnifiedSystemCollector;
  private registry: Registry;
  private lastCpuInfo: any;
  private lastNetworkStats: any;
  
  // Prometheus metrics
  private cpuUsageGauge: Gauge;
  private memoryUsageGauge: Gauge;
  private memoryPercentageGauge: Gauge;
  private heapUsageGauge: Gauge;
  private diskUsageGauge: Gauge;
  private networkRxGauge: Gauge;
  private networkTxGauge: Gauge;
  private eventLoopLatencyGauge: Gauge;
  private uptimeGauge: Gauge;
  
  private constructor() {
    this.registry = monitoringRegistry.getPrometheusRegistry();
    this.initializeMetrics();
  }
  
  static getInstance(): UnifiedSystemCollector {
    if (!this.instance) {
      this.instance = new UnifiedSystemCollector();
    }
    return this.instance;
  }
  
  private initializeMetrics(): void {
    this.cpuUsageGauge = new Gauge({
      name: 'system_cpu_usage_percent',
      help: 'CPU usage percentage',
      registers: [this.registry]
    });
    
    this.memoryUsageGauge = new Gauge({
      name: 'system_memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry]
    });
    
    this.memoryPercentageGauge = new Gauge({
      name: 'system_memory_usage_percent',
      help: 'Memory usage percentage',
      registers: [this.registry]
    });
    
    this.heapUsageGauge = new Gauge({
      name: 'nodejs_heap_usage_bytes',
      help: 'Node.js heap usage in bytes',
      labelNames: ['type'],
      registers: [this.registry]
    });
    
    this.diskUsageGauge = new Gauge({
      name: 'system_disk_usage_bytes',
      help: 'Disk usage in bytes',
      labelNames: ['type'],
      registers: [this.registry]
    });
    
    this.networkRxGauge = new Gauge({
      name: 'system_network_rx_bytes',
      help: 'Network received bytes',
      registers: [this.registry]
    });
    
    this.networkTxGauge = new Gauge({
      name: 'system_network_tx_bytes',
      help: 'Network transmitted bytes',
      registers: [this.registry]
    });
    
    this.eventLoopLatencyGauge = new Gauge({
      name: 'nodejs_event_loop_latency_ms',
      help: 'Event loop latency in milliseconds',
      registers: [this.registry]
    });
    
    this.uptimeGauge = new Gauge({
      name: 'process_uptime_seconds',
      help: 'Process uptime in seconds',
      registers: [this.registry]
    });
  }
  
  /**
   * Collect all system metrics in one pass
   */
  async collectMetrics(): Promise<SystemMetrics> {
    const [cpu, memory, disk, network, eventLoop] = await Promise.all([
      this.collectCPUMetrics(),
      this.collectMemoryMetrics(),
      this.collectDiskMetrics(),
      this.collectNetworkMetrics(),
      this.collectEventLoopMetrics()
    ]);
    
    const process = this.collectProcessMetrics();
    
    const metrics: SystemMetrics = {
      cpu,
      memory,
      disk,
      network,
      eventLoop,
      process
    };
    
    // Update Prometheus metrics
    this.updatePrometheusMetrics(metrics);
    
    // Cache metrics for quick access
    monitoringRegistry.updateCache('system-metrics', metrics, 30);
    
    return metrics;
  }
  
  private async collectCPUMetrics(): Promise<SystemMetrics['cpu']> {
    const cpus = os.cpus();
    const loadAverage = os.loadavg();
    
    // Calculate CPU usage
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });
    
    const usage = this.lastCpuInfo
      ? this.calculateCPUPercentage(totalIdle, totalTick)
      : 0;
    
    this.lastCpuInfo = { totalIdle, totalTick };
    
    return {
      usage,
      cores: cpus.length,
      loadAverage
    };
  }
  
  private calculateCPUPercentage(totalIdle: number, totalTick: number): number {
    const idleDiff = totalIdle - this.lastCpuInfo.totalIdle;
    const totalDiff = totalTick - this.lastCpuInfo.totalTick;
    return totalDiff > 0 ? 100 - (100 * idleDiff / totalDiff) : 0;
  }
  
  private async collectMemoryMetrics(): Promise<SystemMetrics['memory']> {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memUsage = process.memoryUsage();
    
    return {
      total: totalMemory,
      used: usedMemory,
      free: freeMemory,
      percentage: (usedMemory / totalMemory) * 100,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss
    };
  }
  
  private async collectDiskMetrics(): Promise<SystemMetrics['disk']> {
    try {
      // Try to get disk usage from df command (Linux/Mac)
      if (process.platform !== 'win32') {
        const { execSync } = require('child_process');
        const output = execSync('df -k /').toString();
        const lines = output.trim().split('\n');
        
        if (lines.length > 1) {
          const parts = lines[1].split(/\s+/);
          const total = parseInt(parts[1]) * 1024;
          const used = parseInt(parts[2]) * 1024;
          const free = parseInt(parts[3]) * 1024;
          
          return {
            total,
            used,
            free,
            percentage: (used / total) * 100
          };
        }
      }
    } catch (error) {
      logger.debug('Could not collect disk metrics:', error);
    }
    
    return {};
  }
  
  private async collectNetworkMetrics(): Promise<SystemMetrics['network']> {
    try {
      // Read network stats from /proc/net/dev (Linux)
      if (process.platform === 'linux') {
        const data = await readFile('/proc/net/dev', 'utf8');
        const lines = data.trim().split('\n');
        
        let totalRx = 0;
        let totalTx = 0;
        let totalRxPackets = 0;
        let totalTxPackets = 0;
        
        for (let i = 2; i < lines.length; i++) {
          const line = lines[i].trim();
          const parts = line.split(/\s+/);
          
          if (parts.length >= 10) {
            totalRx += parseInt(parts[1]) || 0;
            totalRxPackets += parseInt(parts[2]) || 0;
            totalTx += parseInt(parts[9]) || 0;
            totalTxPackets += parseInt(parts[10]) || 0;
          }
        }
        
        const stats = {
          rxBytes: totalRx,
          txBytes: totalTx,
          rxPackets: totalRxPackets,
          txPackets: totalTxPackets
        };
        
        // Calculate rates if we have previous stats
        if (this.lastNetworkStats) {
          const timeDiff = Date.now() - this.lastNetworkStats.timestamp;
          stats.rxBytes = (totalRx - this.lastNetworkStats.rxBytes) / (timeDiff / 1000);
          stats.txBytes = (totalTx - this.lastNetworkStats.txBytes) / (timeDiff / 1000);
        }
        
        this.lastNetworkStats = {
          ...stats,
          timestamp: Date.now()
        };
        
        return stats;
      }
    } catch (error) {
      logger.debug('Could not collect network metrics:', error);
    }
    
    return {};
  }
  
  private async collectEventLoopMetrics(): Promise<SystemMetrics['eventLoop']> {
    const metrics: SystemMetrics['eventLoop'] = {};
    
    try {
      // Measure event loop latency
      const start = process.hrtime.bigint();
      await new Promise(resolve => setImmediate(resolve));
      const end = process.hrtime.bigint();
      metrics.latency = Number(end - start) / 1000000; // Convert to ms
      
      // Try to get event loop utilization if available (Node.js 14+)
      if (performance && (performance as any).eventLoopUtilization) {
        const elu = (performance as any).eventLoopUtilization();
        metrics.utilization = elu.utilization * 100;
      }
    } catch (error) {
      logger.debug('Could not collect event loop metrics:', error);
    }
    
    return metrics;
  }
  
  private collectProcessMetrics(): SystemMetrics['process'] {
    return {
      uptime: process.uptime(),
      pid: process.pid,
      ppid: process.ppid,
      nodeVersion: process.version
    };
  }
  
  private updatePrometheusMetrics(metrics: SystemMetrics): void {
    // CPU metrics
    this.cpuUsageGauge.set(metrics.cpu.usage);
    
    // Memory metrics
    this.memoryUsageGauge.set({ type: 'used' }, metrics.memory.used);
    this.memoryUsageGauge.set({ type: 'free' }, metrics.memory.free);
    this.memoryUsageGauge.set({ type: 'total' }, metrics.memory.total);
    this.memoryPercentageGauge.set(metrics.memory.percentage);
    
    // Heap metrics
    this.heapUsageGauge.set({ type: 'used' }, metrics.memory.heapUsed);
    this.heapUsageGauge.set({ type: 'total' }, metrics.memory.heapTotal);
    this.heapUsageGauge.set({ type: 'external' }, metrics.memory.external);
    this.heapUsageGauge.set({ type: 'rss' }, metrics.memory.rss);
    
    // Disk metrics
    if (metrics.disk.used !== undefined) {
      this.diskUsageGauge.set({ type: 'used' }, metrics.disk.used);
      this.diskUsageGauge.set({ type: 'free' }, metrics.disk.free || 0);
      this.diskUsageGauge.set({ type: 'total' }, metrics.disk.total || 0);
    }
    
    // Network metrics
    if (metrics.network.rxBytes !== undefined) {
      this.networkRxGauge.set(metrics.network.rxBytes);
      this.networkTxGauge.set(metrics.network.txBytes || 0);
    }
    
    // Event loop metrics
    if (metrics.eventLoop.latency !== undefined) {
      this.eventLoopLatencyGauge.set(metrics.eventLoop.latency);
    }
    
    // Process metrics
    this.uptimeGauge.set(metrics.process.uptime);
  }
  
  /**
   * Start unified system metrics collection
   */
  start(interval: number = 30000): void {
    monitoringRegistry.registerCollector(
      'unified-system-metrics',
      interval,
      async () => {
        await this.collectMetrics();
      }
    );
    
    logger.info(`Started unified system metrics collector with ${interval}ms interval`);
  }
  
  /**
   * Stop metrics collection
   */
  stop(): void {
    monitoringRegistry.unregisterCollector('unified-system-metrics');
  }
}

// Export singleton instance
export const systemCollector = UnifiedSystemCollector.getInstance();