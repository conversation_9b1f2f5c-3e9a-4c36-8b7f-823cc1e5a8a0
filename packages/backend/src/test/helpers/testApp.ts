/**
 * Test App Helper
 *
 * Creates a test Express application with all necessary middleware
 * and routes for integration testing.
 */

import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { errorHandler } from '../../middleware/errorHandler';
import { requestLogger } from '../../middleware/requestLogger';

// Import routes
import authRoutes from '../../routes/auth';
import userRoutes from '../../routes/users';
import projectRoutes from '../../routes/projects';
import { createImageRoutes } from '../../routes/images';

/**
 * Create a test Express application
 */
export function createTestApp(): Express {
  const app = express();

  // Basic middleware
  app.use(
    helmet({
      contentSecurityPolicy: false, // Disable for testing
      crossOriginEmbedderPolicy: false,
    })
  );

  app.use(
    cors({
      origin: true, // Allow all origins in tests
      credentials: true,
    })
  );

  app.use(compression());
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // Request logging (but quiet in tests)
  app.use(requestLogger);

  // Health check endpoint
  app.get('/health', (_req: Request, _res: Response) => {
    // Use shared response helpers
    // import { sendSuccess, sendError } from '@shared/utils/apiHelpers';
    // sendSuccess(res, data, message);
  });

  // API routes
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/projects', projectRoutes);
  app.use('/api/images', createImageRoutes());
  // Note: Segmentation functionality is now handled in image routes

  // Error handling
  app.use(errorHandler);

  return app;
}

/**
 * Create a minimal test app for specific route testing
 */
export function createMinimalTestApp(routes: { path: string; router: express.Router }[]): Express {
  const app = express();

  // Minimal middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Add provided routes
  routes.forEach(({ path, router }) => {
    app.use(path, router);
  });

  // Basic error handler
  app.use((err: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
    res.status(err.status || 500).json({
      error: err.message || 'Internal Server Error',
      ...(process.env.NODE_ENV === 'test' && { stack: err.stack }),
    });
  });

  return app;
}

/**
 * Create test app with custom middleware
 */
export function createCustomTestApp(
  middleware: express.RequestHandler[] = [],
  routes: { path: string; router: express.Router }[] = []
): Express {
  const app = express();

  // Basic middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Add custom middleware
  middleware.forEach((mw) => app.use(mw));

  // Add routes
  routes.forEach(({ path, router }) => {
    app.use(path, router);
  });

  // Error handler
  app.use(errorHandler);

  return app;
}

export default createTestApp;
