/**
 * Secure Secrets Management
 *
 * This module provides secure loading and validation of sensitive environment variables.
 * It ensures all required secrets are present and properly formatted.
 */

import { createHash, randomBytes } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

export interface SecretConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };
  session: {
    secret: string;
    maxAge: number;
  };
  encryption: {
    key: Buffer;
    algorithm: string;
  };
  database: {
    url: string;
    sslEnabled: boolean;
  };
}

export class SecretsManager {
  private static instance: SecretsManager;
  private config: SecretConfig | null = null;
  private readonly requiredSecrets = [
    'JWT_SECRET',
    'SESSION_SECRET',
    'REFRESH_TOKEN_SECRET',
    'ENCRYPTION_KEY',
    'DATABASE_URL',
  ];

  private constructor() {}

  static getInstance(): SecretsManager {
    if (!SecretsManager.instance) {
      SecretsManager.instance = new SecretsManager();
    }
    return SecretsManager.instance;
  }

  /**
   * Initialize and validate all secrets
   */
  initialize(): SecretConfig {
    if (this.config) {
      return this.config;
    }

    this.validateEnvironment();
    this.config = this.loadSecrets();
    this.auditSecrets();

    return this.config;
  }

  /**
   * Validate that all required environment variables are present
   */
  private validateEnvironment(): void {
    const missing: string[] = [];
    const warnings: string[] = [];

    for (const secret of this.requiredSecrets) {
      if (!process.env[secret]) {
        if (process.env.NODE_ENV === 'production') {
          missing.push(secret);
        } else {
          warnings.push(secret);
        }
      }
    }

    if (missing.length > 0) {
      throw new Error(
        `CRITICAL: Missing required secrets in production: ${missing.join(', ')}\n` +
          'Please set these environment variables before starting the application.'
      );
    }

    if (warnings.length > 0) {
      console.warn(
        `⚠️  WARNING: Missing secrets in development mode: ${warnings.join(', ')}\n` +
          'Using insecure defaults. NEVER use these in production!'
      );
    }

    // Validate secret strength
    this.validateSecretStrength();
  }

  /**
   * Validate that secrets meet minimum security requirements
   */
  private validateSecretStrength(): void {
    const jwtSecret = process.env.JWT_SECRET || '';
    const sessionSecret = process.env.SESSION_SECRET || '';
    const refreshSecret = process.env.REFRESH_TOKEN_SECRET || '';
    const encryptionKey = process.env.ENCRYPTION_KEY || '';

    if (process.env.NODE_ENV === 'production') {
      // JWT Secret validation
      if (jwtSecret.length < 64) {
        throw new Error('JWT_SECRET must be at least 64 characters in production');
      }

      // Session Secret validation
      if (sessionSecret.length < 32) {
        throw new Error('SESSION_SECRET must be at least 32 characters in production');
      }

      // Refresh Token Secret validation
      if (refreshSecret.length < 64) {
        throw new Error('REFRESH_TOKEN_SECRET must be at least 64 characters in production');
      }

      // Encryption Key validation (should be 32 bytes hex = 64 hex characters)
      if (encryptionKey.length !== 64 || !/^[0-9a-f]+$/i.test(encryptionKey)) {
        throw new Error(
          'ENCRYPTION_KEY must be exactly 32 bytes (64 hex characters) in production'
        );
      }

      // Check for default/weak secrets
      const weakSecrets = ['your-secret-key', 'change-this', 'secret', 'password', 'default'];

      for (const weak of weakSecrets) {
        if (
          jwtSecret.toLowerCase().includes(weak) ||
          sessionSecret.toLowerCase().includes(weak) ||
          refreshSecret.toLowerCase().includes(weak)
        ) {
          throw new Error('CRITICAL: Weak or default secrets detected in production!');
        }
      }
    }
  }

  /**
   * Load and parse secrets with fallbacks for development
   */
  private loadSecrets(): SecretConfig {
    const isDev = process.env.NODE_ENV !== 'production';

    return {
      jwt: {
        secret: process.env.JWT_SECRET || (isDev ? this.generateDevSecret('jwt') : ''),
        expiresIn: process.env.JWT_EXPIRES_IN || '15m',
        refreshSecret:
          process.env.REFRESH_TOKEN_SECRET || (isDev ? this.generateDevSecret('refresh') : ''),
        refreshExpiresIn: process.env.REFRESH_TOKEN_EXPIRY || '7d',
      },
      session: {
        secret: process.env.SESSION_SECRET || (isDev ? this.generateDevSecret('session') : ''),
        maxAge: parseInt(process.env.SESSION_TIMEOUT || '3600', 10) * 1000,
      },
      encryption: {
        key: this.loadEncryptionKey(),
        algorithm: 'aes-256-gcm',
      },
      database: {
        url: process.env.DATABASE_URL || '',
        sslEnabled: process.env.DB_SSL === 'true',
      },
    };
  }

  /**
   * Load encryption key from environment or generate for development
   */
  private loadEncryptionKey(): Buffer {
    const key = process.env.ENCRYPTION_KEY;

    if (key) {
      return Buffer.from(key, 'hex');
    }

    if (process.env.NODE_ENV !== 'production') {
      console.warn('⚠️  Using generated encryption key for development. Do not use in production!');
      return randomBytes(32);
    }

    throw new Error('ENCRYPTION_KEY is required in production');
  }

  /**
   * Generate deterministic development secrets (insecure, for dev only!)
   */
  private generateDevSecret(type: string): string {
    const warning = `DEV_ONLY_INSECURE_${type.toUpperCase()}_SECRET_DO_NOT_USE_IN_PRODUCTION`;
    return createHash('sha256').update(warning).digest('base64');
  }

  /**
   * Audit secrets usage and log security events
   */
  private auditSecrets(): void {
    const auditLog = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      secretsLoaded: this.requiredSecrets.map((s) => ({
        name: s,
        present: !!process.env[s],
        length: process.env[s]?.length || 0,
      })),
      warnings: [] as string[],
    };

    // Check for potential issues
    if (process.env.NODE_ENV === 'production') {
      if (process.env.JWT_SECRET === process.env.REFRESH_TOKEN_SECRET) {
        auditLog.warnings.push('JWT_SECRET and REFRESH_TOKEN_SECRET should be different');
      }

      // Log to audit file (create audit directory if needed)
      const auditDir = path.join(process.cwd(), 'logs', 'audit');
      if (!fs.existsSync(auditDir)) {
        fs.mkdirSync(auditDir, { recursive: true });
      }

      const auditFile = path.join(auditDir, `secrets-${Date.now()}.json`);
      fs.writeFileSync(auditFile, JSON.stringify(auditLog, null, 2));
    }

    console.log('🔐 Secrets loaded and validated successfully');
  }

  /**
   * Rotate secrets (for use in rotation script)
   */
  async rotateSecrets(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Secret rotation must be performed through the rotation script');
    }

    console.log('🔄 Starting secret rotation...');

    // Generate new secrets
    const newSecrets = {
      JWT_SECRET: randomBytes(64).toString('base64'),
      SESSION_SECRET: randomBytes(48).toString('base64'),
      REFRESH_TOKEN_SECRET: randomBytes(64).toString('base64'),
      ENCRYPTION_KEY: randomBytes(32).toString('hex'),
    };

    // Log rotation event
    const rotationLog = {
      timestamp: new Date().toISOString(),
      action: 'SECRET_ROTATION',
      secrets: Object.keys(newSecrets),
    };

    console.log('📝 Rotation log:', rotationLog);
    console.log('🔑 New secrets generated (store these securely):');
    console.log(newSecrets);
  }

  /**
   * Get the current configuration
   */
  getConfig(): SecretConfig {
    if (!this.config) {
      throw new Error('Secrets not initialized. Call initialize() first.');
    }
    return this.config;
  }

  /**
   * Validate a specific secret
   */
  validateSecret(name: string, value: string): boolean {
    switch (name) {
      case 'JWT_SECRET':
      case 'REFRESH_TOKEN_SECRET':
        return value.length >= 64;
      case 'SESSION_SECRET':
        return value.length >= 32;
      case 'ENCRYPTION_KEY':
        return value.length === 64 && /^[0-9a-f]+$/i.test(value);
      default:
        return value.length > 0;
    }
  }

  /**
   * Check if running with secure secrets
   */
  isSecure(): boolean {
    if (process.env.NODE_ENV !== 'production') {
      return true; // Development mode is considered "secure enough"
    }

    return this.requiredSecrets.every(
      (secret) => process.env[secret] && this.validateSecret(secret, process.env[secret]!)
    );
  }
}

// Export singleton instance
export const secretsManager = SecretsManager.getInstance();

// Export initialization function
export function initializeSecrets(): SecretConfig {
  return secretsManager.initialize();
}

// Export validation function
export function validateSecrets(): boolean {
  return secretsManager.isSecure();
}
