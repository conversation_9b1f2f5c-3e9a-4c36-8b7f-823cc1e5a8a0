/**
 * Consolidated Performance Monitoring Middleware
 *
 * Single source of truth for all performance monitoring functionality.
 * Consolidates features from multiple performance monitoring systems:
 * - performanceCoordinator.ts (837 lines) - Unified coordination & batching
 * - performanceTracker.ts (745 lines) - Enhanced tracking & Prometheus metrics  
 * - performanceOptimizer.ts (703 lines) - Optimization insights & recommendations
 * - performanceMonitor.enhanced.ts (548 lines) - Real-time monitoring
 * - performanceMonitor service (446 lines) - Service layer monitoring
 * 
 * Total consolidated: ~3,000+ lines → ~500 lines
 */

import { Request, Response, NextFunction } from 'express';
import { EventEmitter } from 'events';
import os from 'os';
import { performance } from 'perf_hooks';
import logger from '../utils/logger';
import { Counter, Histogram, Gauge } from 'prom-client';
import { monitoringRegistry } from '../monitoring/UnifiedMonitoringRegistry';

// Core interfaces
interface ApiMetric {
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  timestamp: Date;
  userId?: string;
  memoryUsed?: number;
  cpuUsage?: number;
}

interface PerformanceInsight {
  id: string;
  type: 'bottleneck' | 'optimization' | 'anomaly' | 'recommendation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  timestamp: number;
  confidence: number; // 0-1
}

interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: {
    total: number;
    used: number;
    percentage: number;
  };
  heapUsage: {
    total: number;
    used: number;
    percentage: number;
  };
  eventLoopLag: number;
  timestamp: number;
}

// Performance monitoring options
export interface PerformanceMonitoringOptions {
  enabled?: boolean;
  enablePrometheus?: boolean;
  enableMemoryMonitoring?: boolean;
  enableDatabaseMonitoring?: boolean;
  enableResponseHeaders?: boolean;
  enableSlowRequestLogging?: boolean;
  enableInsights?: boolean;
  enableOptimizations?: boolean;
  enableBatching?: boolean;
  skipInTest?: boolean;
  skipPaths?: string[];
  slowRequestThreshold?: number; // ms
  memoryCheckInterval?: number; // ms
  memoryWarningThreshold?: number; // percentage
  memoryErrorThreshold?: number; // percentage
  gcThreshold?: number; // percentage
  batchSize?: number;
  flushIntervalMs?: number;
  insightHistoryLimit?: number;
}

// Default configuration
const DEFAULT_OPTIONS: PerformanceMonitoringOptions = {
  enabled: true,
  enablePrometheus: true,
  enableMemoryMonitoring: true,
  enableDatabaseMonitoring: true,
  enableResponseHeaders: true,
  enableSlowRequestLogging: true,
  enableInsights: true,
  enableOptimizations: false, // Disabled by default to prevent performance impact
  enableBatching: true,
  skipInTest: true,
  skipPaths: ['/metrics', '/health', '/ready', '/favicon.ico'],
  slowRequestThreshold: 1000, // 1 second
  memoryCheckInterval: 30000, // 30 seconds
  memoryWarningThreshold: 70,
  memoryErrorThreshold: 85,
  gcThreshold: 80,
  batchSize: 100,
  flushIntervalMs: 5000, // 5 seconds
  insightHistoryLimit: 50,
};

// Enhanced Performance Monitor with consolidated features
class PerformanceMonitor extends EventEmitter {
  private memoryCheckTimer?: NodeJS.Timeout;
  private systemMetricsTimer?: NodeJS.Timeout;
  private flushTimer?: NodeJS.Timeout;
  private lastMemoryCheck = Date.now();
  private eventLoopLag = 0;
  private metricsBatch: ApiMetric[] = [];
  private insightsHistory: PerformanceInsight[] = [];
  private systemMetrics: SystemMetrics | null = null;
  private options: PerformanceMonitoringOptions = DEFAULT_OPTIONS;

  constructor(options: PerformanceMonitoringOptions = {}) {
    super();
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.startEventLoopMonitoring();
  }

  // Start all monitoring systems
  startMonitoring(options: PerformanceMonitoringOptions) {
    this.options = { ...this.options, ...options };
    
    if (this.options.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }
    
    if (this.options.enableBatching) {
      this.startBatchProcessor();
    }

    this.startSystemMetricsCollection();
  }

  // Memory monitoring with enhanced features
  private startMemoryMonitoring() {
    if (this.memoryCheckTimer) return; // Already running

    this.memoryCheckTimer = setInterval(() => {
      const usage = process.memoryUsage();
      const heapUsedPercent = (usage.heapUsed / usage.heapTotal) * 100;

      if (heapUsedPercent > this.options.memoryErrorThreshold!) {
        logger.error('Critical memory pressure detected', {
          heapUsedPercent: heapUsedPercent.toFixed(2),
          heapUsed: formatBytes(usage.heapUsed),
          heapTotal: formatBytes(usage.heapTotal),
          eventLoopLag: this.eventLoopLag,
        });
        this.emit('memoryPressure', { level: 'critical', usage });

        // Generate performance insight
        if (this.options.enableInsights) {
          this.generateInsight({
            type: 'bottleneck',
            severity: 'critical',
            title: 'Critical Memory Pressure',
            description: `Heap usage at ${heapUsedPercent.toFixed(2)}%`,
            impact: 'Application may become unresponsive or crash',
            recommendation: 'Investigate memory leaks and optimize resource usage',
          });
        }

        // Log memory pressure event instead of manual GC
        // Manual GC can interfere with Node.js optimization patterns
        if (heapUsedPercent > this.options.gcThreshold!) {
          logger.warn('Memory pressure threshold exceeded', {
            threshold: `${this.options.gcThreshold}%`,
            current: `${heapUsedPercent.toFixed(2)}%`,
            suggestion: 'Consider optimizing memory usage patterns',
          });
          this.emit('memoryPressureThreshold', { level: 'gc-threshold', usage, heapUsedPercent });
        }
      } else if (heapUsedPercent > this.options.memoryWarningThreshold!) {
        logger.warn('High memory usage detected', {
          heapUsedPercent: heapUsedPercent.toFixed(2),
          heapUsed: formatBytes(usage.heapUsed),
          heapTotal: formatBytes(usage.heapTotal),
          eventLoopLag: this.eventLoopLag,
        });
        this.emit('memoryPressure', { level: 'warning', usage });

        if (this.options.enableInsights) {
          this.generateInsight({
            type: 'optimization',
            severity: 'medium',
            title: 'High Memory Usage',
            description: `Heap usage at ${heapUsedPercent.toFixed(2)}%`,
            impact: 'Performance may be degraded',
            recommendation: 'Consider implementing memory optimization strategies',
          });
        }
      }

      this.lastMemoryCheck = Date.now();
    }, this.options.memoryCheckInterval);
  }

  // Event loop lag monitoring
  private startEventLoopMonitoring() {
    let lastCheck = performance.now();
    setInterval(() => {
      const now = performance.now();
      const expectedDelay = 100;
      const actualDelay = now - lastCheck;
      this.eventLoopLag = Math.max(0, actualDelay - expectedDelay);
      lastCheck = now;

      // Alert on high event loop lag
      if (this.eventLoopLag > 50 && this.options.enableInsights) {
        this.generateInsight({
          type: 'bottleneck',
          severity: this.eventLoopLag > 100 ? 'high' : 'medium',
          title: 'Event Loop Lag Detected',
          description: `Event loop lag: ${this.eventLoopLag.toFixed(2)}ms`,
          impact: 'Request processing may be delayed',
          recommendation: 'Identify and optimize blocking operations',
        });
      }
    }, 100);
  }

  // System metrics collection
  private startSystemMetricsCollection() {
    if (this.systemMetricsTimer) return;

    this.systemMetricsTimer = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Every 30 seconds
  }

  private collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const osMemory = {
      total: os.totalmem(),
      free: os.freemem(),
    };

    this.systemMetrics = {
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
      memoryUsage: {
        total: osMemory.total,
        used: osMemory.total - osMemory.free,
        percentage: ((osMemory.total - osMemory.free) / osMemory.total) * 100,
      },
      heapUsage: {
        total: memUsage.heapTotal,
        used: memUsage.heapUsed,
        percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
      },
      eventLoopLag: this.eventLoopLag,
      timestamp: Date.now(),
    };
  }

  // Batch processing for metrics
  private startBatchProcessor() {
    if (this.flushTimer) return;

    this.flushTimer = setInterval(() => {
      this.flushMetricsBatch();
    }, this.options.flushIntervalMs);
  }

  private flushMetricsBatch() {
    if (this.metricsBatch.length === 0) return;

    // Process batched metrics
    const batchCopy = [...this.metricsBatch];
    this.metricsBatch = [];

    // Analyze batch for insights
    if (this.options.enableInsights) {
      this.analyzeBatchForInsights(batchCopy);
    }

    logger.debug(`Processed metrics batch: ${batchCopy.length} metrics`);
  }

  // Add metric to batch
  addMetric(metric: ApiMetric) {
    if (!this.options.enableBatching) return;

    this.metricsBatch.push(metric);

    // Flush if batch is full
    if (this.metricsBatch.length >= this.options.batchSize!) {
      this.flushMetricsBatch();
    }
  }

  // Generate performance insights
  private generateInsight(insight: Omit<PerformanceInsight, 'id' | 'timestamp' | 'confidence'>) {
    const fullInsight: PerformanceInsight = {
      id: `insight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      confidence: 0.8, // Default confidence
      ...insight,
    };

    this.insightsHistory.push(fullInsight);

    // Limit history size
    if (this.insightsHistory.length > this.options.insightHistoryLimit!) {
      this.insightsHistory = this.insightsHistory.slice(-this.options.insightHistoryLimit!);
    }

    this.emit('insight', fullInsight);
    logger.info('Performance insight generated', {
      type: fullInsight.type,
      severity: fullInsight.severity,
      title: fullInsight.title,
    });
  }

  // Analyze batch of metrics for insights
  private analyzeBatchForInsights(metrics: ApiMetric[]) {
    if (metrics.length === 0) return;

    // Calculate average response time
    const avgResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length;
    
    // Identify slow endpoints
    const slowEndpoints = metrics.filter(m => m.responseTime > this.options.slowRequestThreshold!);
    
    if (slowEndpoints.length > metrics.length * 0.1) { // More than 10% slow
      this.generateInsight({
        type: 'bottleneck',
        severity: 'high',
        title: 'High Percentage of Slow Requests',
        description: `${slowEndpoints.length}/${metrics.length} requests exceeded ${this.options.slowRequestThreshold}ms`,
        impact: 'User experience degradation',
        recommendation: 'Investigate and optimize slow endpoints',
      });
    }

    // Check for error rate spikes
    const errorMetrics = metrics.filter(m => m.statusCode >= 400);
    if (errorMetrics.length > metrics.length * 0.05) { // More than 5% errors
      this.generateInsight({
        type: 'anomaly',
        severity: 'high',
        title: 'High Error Rate Detected',
        description: `${errorMetrics.length}/${metrics.length} requests returned errors`,
        impact: 'Service reliability issues',
        recommendation: 'Investigate error patterns and root causes',
      });
    }
  }

  // Get current insights
  getInsights(): PerformanceInsight[] {
    return [...this.insightsHistory];
  }

  // Get current system metrics
  getSystemMetrics(): SystemMetrics | null {
    return this.systemMetrics;
  }

  // Stop all monitoring
  stopMonitoring() {
    if (this.memoryCheckTimer) {
      clearInterval(this.memoryCheckTimer);
      this.memoryCheckTimer = undefined;
    }
    if (this.systemMetricsTimer) {
      clearInterval(this.systemMetricsTimer);
      this.systemMetricsTimer = undefined;
    }
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
  }
}

// Singleton performance monitor instance
const performanceMonitor = new PerformanceMonitor();

// Prometheus metrics (lazy initialization)
let httpRequestDuration: Histogram<string> | undefined;
let httpRequestTotal: Counter<string> | undefined;
let httpActiveRequests: Gauge<string> | undefined;
let memoryUsageGauge: Gauge<string> | undefined;
let _dbQueryDuration: Histogram<string> | undefined;
let _dbQueryTotal: Counter<string> | undefined;
let _dbActiveConnections: Gauge<string> | undefined;

// Initialize Prometheus metrics using unified registry
function initializeMetrics(options: PerformanceMonitoringOptions) {
  if (!options.enablePrometheus) return;

  // Get metrics from unified registry
  const registry = monitoringRegistry.getMetricsRegistry();
  
  httpRequestDuration = registry.getHistogram(
    'http_request_duration_seconds',
    'Duration of HTTP requests in seconds',
    ['method', 'route', 'status_code'],
    [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5]
  );

  httpRequestTotal = registry.getCounter(
    'http_requests_total',
    'Total number of HTTP requests',
    ['method', 'route', 'status_code']
  );

  httpActiveRequests = registry.getGauge(
    'http_active_requests',
    'Number of active HTTP requests'
  );

  if (options.enableMemoryMonitoring) {
    memoryUsageGauge = registry.getGauge(
      'nodejs_memory_usage_bytes',
      'Node.js memory usage',
      ['type']
    );
  }

  if (options.enableDatabaseMonitoring) {
    _dbQueryDuration = registry.getHistogram(
      'db_query_duration_seconds',
      'Duration of database queries in seconds',
      ['operation', 'table'],
      [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2]
    );

    _dbQueryTotal = registry.getCounter(
      'db_queries_total',
      'Total number of database queries',
      ['operation', 'table', 'status']
    );

    _dbActiveConnections = registry.getGauge(
      'db_active_connections',
      'Number of active database connections'
    );
  }
}

// Format bytes to human readable
function formatBytes(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// Main middleware factory
export function createPerformanceMiddleware(options: Partial<PerformanceMonitoringOptions> = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };

  // Skip in test environment if configured
  if (config.skipInTest && process.env.NODE_ENV === 'test') {
    return (req: Request, res: Response, next: NextFunction) => next();
  }

  // Initialize metrics on first use
  if (config.enablePrometheus && !httpRequestDuration) {
    initializeMetrics(config);
  }

  // Start comprehensive monitoring
  performanceMonitor.startMonitoring(config);

  return (req: Request, res: Response, next: NextFunction) => {
    // Skip configured paths
    if (config.skipPaths?.some((path) => req.path.startsWith(path))) {
      return next();
    }

    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    // Track active requests
    if (httpActiveRequests) {
      httpActiveRequests.inc();
    }

    // Store original end function
    const originalEnd = res.end;

    // Override end function to capture metrics
    res.end = function (...args: unknown[]) {
      // Calculate duration
      const endTime = process.hrtime.bigint();
      const durationMs = Number(endTime - startTime) / 1e6;
      const durationSeconds = durationMs / 1000;

      // Get route pattern (Express specific)
      const route = req.route?.path || req.path || 'unknown';
      const method = req.method;
      const statusCode = res.statusCode.toString();

      // Update Prometheus metrics
      if (config.enablePrometheus) {
        httpRequestDuration?.observe({ method, route, status_code: statusCode }, durationSeconds);
        httpRequestTotal?.inc({ method, route, status_code: statusCode });
        httpActiveRequests?.dec();

        // Update memory metrics
        if (config.enableMemoryMonitoring && memoryUsageGauge) {
          const memUsage = process.memoryUsage();
          memoryUsageGauge.set({ type: 'heapUsed' }, memUsage.heapUsed);
          memoryUsageGauge.set({ type: 'heapTotal' }, memUsage.heapTotal);
          memoryUsageGauge.set({ type: 'rss' }, memUsage.rss);
          memoryUsageGauge.set({ type: 'external' }, memUsage.external);
        }
      }

      // Record API metric for performance tracking
      const endMemory = process.memoryUsage();
      const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

      const metric: ApiMetric = {
        endpoint: req.path,
        method: req.method,
        statusCode: res.statusCode,
        responseTime: durationMs,
        timestamp: new Date(),
        userId: (req as Request & { user?: { id: string } }).user?.id,
        memoryUsed: memoryDelta > 0 ? memoryDelta : undefined,
        cpuUsage: performanceMonitor.getSystemMetrics()?.cpuUsage,
      };

      // Add metric to batch processor
      performanceMonitor.addMetric(metric);

      // Add response headers if enabled
      if (config.enableResponseHeaders) {
        res.setHeader('X-Response-Time', `${durationMs.toFixed(2)}ms`);
        if (config.enableMemoryMonitoring && memoryDelta > 0) {
          res.setHeader('X-Memory-Usage', formatBytes(memoryDelta));
        }
      }

      // Log slow requests
      if (config.enableSlowRequestLogging && durationMs > config.slowRequestThreshold!) {
        logger.warn('Slow request detected', {
          method,
          path: req.path,
          route,
          statusCode,
          duration: `${durationMs.toFixed(2)}ms`,
          threshold: `${config.slowRequestThreshold}ms`,
          userId: (req as Request & { user?: { id: string } }).user?.id,
          ip: req.ip,
          userAgent: req.get('user-agent'),
        });
      }

      // Call original end function
      return originalEnd.apply(res, args as Parameters<typeof originalEnd>);
    };

    next();
  };
}

// Export singleton middleware instance with default options
export const performanceMiddleware = createPerformanceMiddleware();

// Export additional utilities and types
export { 
  performanceMonitor, 
  formatBytes, 
  PerformanceMonitor,
  type ApiMetric,
  type PerformanceInsight,
  type SystemMetrics
};

// Utility function to get performance insights (for API endpoints)
export function getPerformanceInsights(): PerformanceInsight[] {
  return performanceMonitor.getInsights();
}

// Utility function to get system metrics (for API endpoints)
export function getCurrentSystemMetrics(): SystemMetrics | null {
  return performanceMonitor.getSystemMetrics();
}

// Cleanup on process exit
process.on('exit', () => {
  performanceMonitor.stopMonitoring();
});

process.on('SIGINT', () => {
  performanceMonitor.stopMonitoring();
  process.exit(0);
});

process.on('SIGTERM', () => {
  performanceMonitor.stopMonitoring();
  process.exit(0);
});
