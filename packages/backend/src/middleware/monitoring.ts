/**
 * Unified Monitoring Middleware
 * Consolidated middleware using the unified monitoring system
 */

import { Request, Response, NextFunction } from 'express';
import { monitoringRegistry } from '../monitoring/UnifiedMonitoringRegistry';
import { logger } from '../utils/logger';

/**
 * Express middleware for HTTP request monitoring
 * Uses the unified monitoring system for consistent metrics collection
 */
export const unifiedMonitoringMiddleware = monitoringRegistry.getExpressMiddleware();

/**
 * Database monitoring middleware wrapper
 * Wraps database queries with monitoring
 */
export function createMonitoredQuery<T = any>(originalQuery: Function) {
  return monitoringRegistry.getDatabaseCollector().createQueryWrapper<T>(originalQuery);
}

/**
 * Custom metrics tracking middleware
 */
export function customMetricsMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Track user sessions if user is authenticated
  if (req.user) {
    const sessionMetric = monitoringRegistry.getMetricsRegistry().getGauge(
      'user_sessions_active',
      'Number of active user sessions'
    );
    // This is a simplified implementation - in production, you'd track actual sessions
    sessionMetric.inc();
  }

  // Track specific routes
  const route = req.route?.path || req.path;
  if (route.includes('/api/projects')) {
    const projectApiMetric = monitoringRegistry.getMetricsRegistry().getCounter(
      'project_api_requests_total',
      'Total number of project API requests',
      ['method', 'endpoint']
    );
    projectApiMetric.labels(req.method, route).inc();
  }

  next();
}

/**
 * Error tracking middleware
 */
export function errorTrackingMiddleware(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const errorMetric = monitoringRegistry.getMetricsRegistry().getCounter(
    'application_errors_total',
    'Total number of application errors',
    ['error_type', 'route', 'method']
  );

  const errorType = error.name || 'UnknownError';
  const route = req.route?.path || req.path;

  errorMetric.labels(errorType, route, req.method).inc();

  logger.error('Application error tracked', {
    error: error.message,
    route,
    method: req.method,
    stack: error.stack,
  });

  next(error);
}

/**
 * Business metrics tracking utilities
 */
export const businessMetrics = {
  trackProjectCreated: () => {
    const metric = monitoringRegistry.getMetricsRegistry().getCounter(
      'projects_created_total',
      'Total number of projects created'
    );
    metric.inc();
  },

  trackImageUploaded: (size: number, type: string) => {
    const uploadMetric = monitoringRegistry.getMetricsRegistry().getHistogram(
      'image_uploads_total',
      'Total number of image uploads',
      ['type']
    );
    const sizeMetric = monitoringRegistry.getMetricsRegistry().getHistogram(
      'upload_size_bytes',
      'Size of uploaded files in bytes',
      ['type'],
      [1024, 10240, 102400, 1048576, 10485760, 104857600]
    );

    uploadMetric.labels(type).observe(1);
    sizeMetric.labels(type).observe(size);
  },

  trackSegmentationRequest: (model: string, imageSize: number) => {
    const segmentationMetric = monitoringRegistry.getMetricsRegistry().getCounter(
      'segmentation_requests_total',
      'Total number of segmentation requests',
      ['model']
    );
    const imageSizeMetric = monitoringRegistry.getMetricsRegistry().getHistogram(
      'segmentation_image_size_pixels',
      'Size of images being segmented in pixels',
      ['model'],
      [100000, 500000, 1000000, 5000000, 10000000, 50000000]
    );

    segmentationMetric.labels(model).inc();
    imageSizeMetric.labels(model).observe(imageSize);
  },

  trackSegmentationComplete: (model: string, duration: number, success: boolean) => {
    const durationMetric = monitoringRegistry.getMetricsRegistry().getHistogram(
      'segmentation_duration_seconds',
      'Duration of segmentation processing in seconds',
      ['model', 'success'],
      [1, 5, 10, 30, 60, 120, 300, 600]
    );

    durationMetric.labels(model, success.toString()).observe(duration);
  },

  trackUserRegistration: () => {
    const metric = monitoringRegistry.getMetricsRegistry().getCounter(
      'user_registrations_total',
      'Total number of user registrations'
    );
    metric.inc();
  },

  trackLoginAttempt: (success: boolean) => {
    const metric = monitoringRegistry.getMetricsRegistry().getCounter(
      'login_attempts_total',
      'Total number of login attempts',
      ['status']
    );
    metric.labels(success ? 'success' : 'failure').inc();
  },
};

/**
 * Performance monitoring utilities
 */
export const performanceMetrics = {
  startTimer: (name: string, labels: Record<string, string> = {}) => {
    const startTime = Date.now();
    
    return {
      end: () => {
        const duration = (Date.now() - startTime) / 1000;
        const metric = monitoringRegistry.getMetricsRegistry().getHistogram(
          `performance_${name}_duration_seconds`,
          `Duration of ${name} operations in seconds`,
          Object.keys(labels),
          [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5]
        );

        metric.labels(...Object.values(labels)).observe(duration);
        return duration;
      }
    };
  },

  recordMemoryUsage: () => {
    const memUsage = process.memoryUsage();
    const heapUsedMetric = monitoringRegistry.getMetricsRegistry().getGauge(
      'memory_heap_used_bytes',
      'Used heap memory in bytes'
    );
    const heapTotalMetric = monitoringRegistry.getMetricsRegistry().getGauge(
      'memory_heap_total_bytes', 
      'Total heap memory in bytes'
    );

    heapUsedMetric.set(memUsage.heapUsed);
    heapTotalMetric.set(memUsage.heapTotal);
  },
};

/**
 * Initialize monitoring middleware and set up periodic metrics collection
 */
export function initializeMonitoring(): void {
  // Set up periodic memory monitoring
  setInterval(() => {
    performanceMetrics.recordMemoryUsage();
  }, 30000); // Every 30 seconds

  logger.info('✅ Unified monitoring middleware initialized');
}