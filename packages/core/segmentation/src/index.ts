/**
 * Core Segmentation Module
 * 
 * This module provides segmentation services for the SpheroSeg application
 * following domain-driven design principles.
 * 
 * Public API:
 * - SegmentationService: Core segmentation business logic
 * - Types: TypeScript interfaces and types
 * - Constants: Segmentation-related constants
 */

// Services
export { SegmentationService } from './services/SegmentationService';

// Types and interfaces
export * from './types';

// Re-export shared kernel types that are commonly used with segmentation
export { Status } from '@spheroseg/shared-kernel';

/**
 * Factory function to create configured segmentation services
 */
export interface SegmentationModuleDependencies {
  segmentationRepository: any;
  queueService: any;
  mlService: any;
  fileService: any;
  eventEmitter: any;
  logger: any;
  config: any;
}

export class SegmentationModule {
  public readonly segmentationService: SegmentationService;

  constructor(dependencies: SegmentationModuleDependencies) {
    this.segmentationService = new SegmentationService(dependencies);
  }
}

export default SegmentationModule;