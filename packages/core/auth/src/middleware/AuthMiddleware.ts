/**
 * Core Authentication Middleware
 * Handles JWT token validation and user authentication
 * Modular implementation following domain-driven design
 */

import { Request, Response, NextFunction } from 'express';
import { UnauthorizedError, BaseError } from '@spheroseg/shared-kernel';

export interface UserPayload {
  userId: string;
  email: string;
  role?: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: UserPayload;
    }
  }
}

// Dependencies that will be injected
interface AuthMiddlewareDependencies {
  tokenService: any;
  logger: any;
}

export class AuthMiddleware {
  constructor(private readonly deps: AuthMiddlewareDependencies) {}

  /**
   * Middleware to authenticate requests using JWT tokens
   */
  public authenticate = (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return res.status(401).json({ 
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
      }

      // Verify the token
      this.verifyTokenAsync(token)
        .then((payload) => {
          if (!payload) {
            return res.status(401).json({ 
              success: false,
              error: 'Invalid or expired token',
              code: 'UNAUTHORIZED'
            });
          }

          // Set user information in request
          req.user = {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
          };

          next();
        })
        .catch((error) => {
          this.deps.logger.error({ message: 'Token verification failed', error });
          return res.status(401).json({ 
            success: false,
            error: 'Authentication failed',
            code: 'UNAUTHORIZED'
          });
        });
    } catch (error) {
      this.deps.logger.error({ message: 'Authentication middleware error', error });
      return res.status(500).json({ 
        success: false,
        error: 'Authentication service error',
        code: 'INTERNAL_ERROR'
      });
    }
  };

  /**
   * Optional authentication middleware - doesn't fail if no token provided
   */
  public optionalAuthenticate = (req: Request, res: Response, next: NextFunction) => {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      // No token provided, continue without authentication
      return next();
    }

    this.verifyTokenAsync(token)
      .then((payload) => {
        if (payload) {
          req.user = {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
          };
        }
        next();
      })
      .catch((error) => {
        // Log error but don't fail the request
        this.deps.logger.warn({ message: 'Optional authentication failed', error });
        next();
      });
  };

  /**
   * Middleware to require specific roles
   */
  public requireRole = (requiredRoles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
      }

      const userRole = req.user.role || 'user';
      
      if (!requiredRoles.includes(userRole)) {
        return res.status(403).json({ 
          success: false,
          error: 'Insufficient permissions',
          code: 'FORBIDDEN'
        });
      }

      next();
    };
  };

  /**
   * Middleware to require admin role
   */
  public requireAdmin = this.requireRole(['admin']);

  /**
   * Extract user ID from authenticated request
   */
  public extractUserId(req: Request): string | null {
    return req.user?.userId || null;
  }

  /**
   * Extract user email from authenticated request
   */
  public extractUserEmail(req: Request): string | null {
    return req.user?.email || null;
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(req: Request): boolean {
    return !!req.user?.userId;
  }

  /**
   * Check if user has specific role
   */
  public hasRole(req: Request, role: string): boolean {
    return req.user?.role === role;
  }

  /**
   * Check if user is admin
   */
  public isAdmin(req: Request): boolean {
    return this.hasRole(req, 'admin');
  }

  /**
   * Async token verification wrapper
   */
  private async verifyTokenAsync(token: string): Promise<any> {
    try {
      return await this.deps.tokenService.verifyAccessToken(token);
    } catch (error) {
      this.deps.logger.debug({ message: 'Token verification error', error });
      return null;
    }
  }
}