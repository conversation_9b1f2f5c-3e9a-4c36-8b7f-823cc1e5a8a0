/**
 * Core Authentication Validators
 * Input validation for authentication operations
 * Modular implementation following domain-driven design
 */

import { ValidationError } from '@spheroseg/shared-kernel';
import type {
  RegisterRequest,
  LoginRequest,
  RefreshTokenRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  VerifyEmailRequest,
} from '../types';

export class AuthValidators {
  /**
   * Validate email format
   */
  private static validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('Invalid email format');
    }

    if (email.length > 254) {
      throw new ValidationError('Email address too long');
    }
  }

  /**
   * Validate password strength
   */
  private static validatePassword(password: string): void {
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      throw new ValidationError('Password too long');
    }

    // Check for at least one uppercase, one lowercase, one number
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);

    if (!hasUppercase || !hasLowercase || !hasNumber) {
      throw new ValidationError(
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      );
    }

    // Check for common weak passwords
    const commonPasswords = [
      'password',
      '12345678',
      'password123',
      'admin123',
      'welcome123',
    ];
    
    if (commonPasswords.includes(password.toLowerCase())) {
      throw new ValidationError('Password is too common, please choose a stronger password');
    }
  }

  /**
   * Validate name format
   */
  private static validateName(name: string): void {
    if (name.length < 1) {
      throw new ValidationError('Name is required');
    }

    if (name.length > 100) {
      throw new ValidationError('Name too long');
    }

    // Only allow letters, numbers, spaces, and basic punctuation
    const nameRegex = /^[a-zA-Z0-9\s\-_.]+$/;
    if (!nameRegex.test(name)) {
      throw new ValidationError('Name contains invalid characters');
    }
  }

  /**
   * Validate token format
   */
  private static validateToken(token: string): void {
    if (!token || typeof token !== 'string') {
      throw new ValidationError('Token is required');
    }

    if (token.length < 10) {
      throw new ValidationError('Invalid token format');
    }

    if (token.length > 2048) {
      throw new ValidationError('Token too long');
    }
  }

  /**
   * Validate registration request
   */
  public static validateRegisterRequest(data: any): RegisterRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { email, password, name } = data;

    // Validate required fields
    if (!email) {
      throw new ValidationError('Email is required');
    }

    if (!password) {
      throw new ValidationError('Password is required');
    }

    if (!name) {
      throw new ValidationError('Name is required');
    }

    // Validate field formats
    this.validateEmail(email);
    this.validatePassword(password);
    this.validateName(name);

    return {
      email: email.toLowerCase().trim(),
      password,
      name: name.trim(),
    };
  }

  /**
   * Validate login request
   */
  public static validateLoginRequest(data: any): LoginRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { email, password } = data;

    // Validate required fields
    if (!email) {
      throw new ValidationError('Email is required');
    }

    if (!password) {
      throw new ValidationError('Password is required');
    }

    // Validate email format
    this.validateEmail(email);

    // Basic password check (don't validate strength for login)
    if (typeof password !== 'string' || password.length < 1) {
      throw new ValidationError('Password is required');
    }

    return {
      email: email.toLowerCase().trim(),
      password,
    };
  }

  /**
   * Validate refresh token request
   */
  public static validateRefreshTokenRequest(data: any): RefreshTokenRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { refreshToken } = data;

    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    this.validateToken(refreshToken);

    return { refreshToken };
  }

  /**
   * Validate forgot password request
   */
  public static validateForgotPasswordRequest(data: any): ForgotPasswordRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { email } = data;

    if (!email) {
      throw new ValidationError('Email is required');
    }

    this.validateEmail(email);

    return {
      email: email.toLowerCase().trim(),
    };
  }

  /**
   * Validate reset password request
   */
  public static validateResetPasswordRequest(data: any): ResetPasswordRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { token, newPassword } = data;

    if (!token) {
      throw new ValidationError('Reset token is required');
    }

    if (!newPassword) {
      throw new ValidationError('New password is required');
    }

    this.validateToken(token);
    this.validatePassword(newPassword);

    return {
      token,
      newPassword,
    };
  }

  /**
   * Validate change password request
   */
  public static validateChangePasswordRequest(data: any): ChangePasswordRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { currentPassword, newPassword } = data;

    if (!currentPassword) {
      throw new ValidationError('Current password is required');
    }

    if (!newPassword) {
      throw new ValidationError('New password is required');
    }

    // Basic validation for current password
    if (typeof currentPassword !== 'string' || currentPassword.length < 1) {
      throw new ValidationError('Current password is required');
    }

    // Full validation for new password
    this.validatePassword(newPassword);

    // Ensure passwords are different
    if (currentPassword === newPassword) {
      throw new ValidationError('New password must be different from current password');
    }

    return {
      currentPassword,
      newPassword,
    };
  }

  /**
   * Validate email verification request
   */
  public static validateVerifyEmailRequest(data: any): VerifyEmailRequest {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Invalid request data');
    }

    const { token } = data;

    if (!token) {
      throw new ValidationError('Verification token is required');
    }

    this.validateToken(token);

    return { token };
  }
}