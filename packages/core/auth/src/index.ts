/**
 * Core Authentication Module
 * 
 * This module provides authentication services, middleware, and validators
 * for the SpheroSeg application following domain-driven design principles.
 * 
 * Public API:
 * - AuthService: Core authentication business logic
 * - TokenService: JWT token management
 * - AuthMiddleware: Express middleware for authentication
 * - AuthValidators: Input validation for auth operations
 * - Types: TypeScript interfaces and types
 */

// Services
export { AuthService } from './services/AuthService';
export { TokenService } from './services/TokenService';

// Middleware
export { AuthMiddleware, UserPayload } from './middleware/AuthMiddleware';

// Validators
export { AuthValidators } from './validators/AuthValidators';

// Types and interfaces
export * from './types';

// Re-export shared kernel types that are commonly used with auth
export { TokenType } from '@spheroseg/shared-kernel';

/**
 * Factory function to create configured auth services
 * This will be used by the infrastructure layer to inject dependencies
 */
export interface AuthModuleDependencies {
  userRepository: any;
  tokenService: any;
  emailService: any;
  cacheService: any;
  logger: any;
  config: any;
  prisma: any;
  securityConfig: any;
  keyManager?: any;
}

export class AuthModule {
  public readonly authService: AuthService;
  public readonly tokenService: TokenService;
  public readonly authMiddleware: AuthMiddleware;

  constructor(dependencies: AuthModuleDependencies) {
    // Initialize token service first as it's needed by auth service
    this.tokenService = new TokenService({
      prisma: dependencies.prisma,
      config: dependencies.config,
      securityConfig: dependencies.securityConfig,
      logger: dependencies.logger,
      keyManager: dependencies.keyManager,
    });

    // Initialize auth service with token service dependency
    this.authService = new AuthService({
      userRepository: dependencies.userRepository,
      tokenService: this.tokenService,
      emailService: dependencies.emailService,
      cacheService: dependencies.cacheService,
      logger: dependencies.logger,
      config: dependencies.config,
    });

    // Initialize auth middleware
    this.authMiddleware = new AuthMiddleware({
      tokenService: this.tokenService,
      logger: dependencies.logger,
    });
  }
}

export default AuthModule;