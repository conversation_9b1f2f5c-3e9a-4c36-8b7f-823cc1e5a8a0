# Core Modules Integration Example

This document shows how the new modular architecture integrates with the existing system.

## Module Structure

```
packages/
├── core/                    # Business domains
│   ├── auth/               # Authentication domain
│   │   ├── src/
│   │   │   ├── services/   # AuthService, TokenService
│   │   │   ├── middleware/ # AuthMiddleware
│   │   │   ├── validators/ # AuthValidators
│   │   │   └── types/      # Domain types
│   │   └── index.ts        # Public API
│   └── segmentation/       # Segmentation domain
│       ├── src/
│       │   ├── services/   # SegmentationService
│       │   └── types/      # Domain types
│       └── index.ts        # Public API
├── infrastructure/         # Technical implementations
│   ├── database/          # Repository implementations
│   ├── messaging/         # Queue implementations
│   └── monitoring/        # Logging implementations
└── shared-kernel/         # Minimal shared types
    └── src/
        ├── types/         # Common types
        └── errors/        # Base error classes
```

## Usage Example

```typescript
// In backend application setup (e.g., packages/backend/src/app.ts)
import { AuthModule, SegmentationModule } from '@spheroseg/core';

// Dependencies would be injected by infrastructure layer
const authModule = new AuthModule({
  userRepository: userRepo,
  tokenService: tokenSvc,
  emailService: emailSvc,
  cacheService: cacheSvc,
  logger,
  config,
  prisma,
  securityConfig,
});

const segmentationModule = new SegmentationModule({
  segmentationRepository: segRepo,
  queueService: queueSvc,
  mlService: mlSvc,
  fileService: fileSvc,
  eventEmitter,
  logger,
  config,
});

// Use in routes
app.post('/auth/login', authModule.authMiddleware.authenticate, async (req, res) => {
  try {
    const result = await authModule.authService.login(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(error.statusCode || 500).json({ success: false, error: error.message });
  }
});

app.post('/segmentation/start', authModule.authMiddleware.authenticate, async (req, res) => {
  try {
    const result = await segmentationModule.segmentationService.startSegmentation(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(error.statusCode || 500).json({ success: false, error: error.message });
  }
});
```

## Migration Strategy

### Phase 1 (Current) ✅
- [x] Create module structure
- [x] Move authentication module
- [x] Move segmentation module 
- [x] Setup proper exports

### Phase 2 (Next)
- [ ] Move and create user repository implementations
- [ ] Move queue service to infrastructure/messaging
- [ ] Move file services to infrastructure/storage
- [ ] Update backend imports to use new modules

### Phase 3 (Later)
- [ ] Move projects domain
- [ ] Move users domain
- [ ] Complete infrastructure implementations
- [ ] Remove old service files

## Benefits

1. **Clear Separation**: Business logic separated from infrastructure
2. **Dependency Injection**: Testable, flexible dependencies
3. **Type Safety**: Strong TypeScript interfaces between layers
4. **Reusability**: Modules can be used in different contexts
5. **Maintainability**: Clear boundaries and responsibilities

## Current State

The modular structure is established and ready for incremental migration. The existing backend can start using these modules immediately by:

1. Installing the new packages as workspace dependencies
2. Creating dependency injection setup
3. Gradually replacing direct service imports with module usage
4. Moving repository and infrastructure implementations