name: Security Scanning

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

permissions:
  contents: read
  security-events: write

jobs:
  # CodeQL Analysis
  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        language: ['javascript', 'typescript', 'python']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  # Container Scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    strategy:
      matrix:
        dockerfile: 
          - path: packages/backend/Dockerfile
            name: backend
          - path: packages/frontend/Dockerfile.prod
            name: frontend
          - path: packages/ml/Dockerfile
            name: ml
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        run: |
          docker build -f ${{ matrix.dockerfile.path }} -t spheroseg-${{ matrix.dockerfile.name }}:scan .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: spheroseg-${{ matrix.dockerfile.name }}:scan
          format: 'sarif'
          output: 'trivy-${{ matrix.dockerfile.name }}.sarif'
          severity: 'CRITICAL,HIGH,MEDIUM'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.dockerfile.name }}.sarif'

      - name: Run Snyk container test
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: spheroseg-${{ matrix.dockerfile.name }}:scan
          args: --severity-threshold=high

  # Secret Scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD

  # Dependency Scanning
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: |
          # Create security reports directory
          mkdir -p security-reports
          
          # Run comprehensive security audit
          npm audit --json > security-reports/npm-audit-full.json || true
          npm audit --production --json > security-reports/npm-audit-production.json || true
          
          # Strict security check - NO bypass allowed
          echo "🔍 Running strict security audit (production dependencies)..."
          if ! npm audit --production --audit-level=high; then
            echo "::error::❌ Critical security vulnerabilities found in production dependencies!"
            echo "::error::🚨 BUILD BLOCKED - Security vulnerabilities must be fixed before deployment"
            echo "::error::📋 Run 'npm audit' locally to see details and 'npm audit fix' to resolve"
            exit 1
          fi
          
          echo "🔍 Running development dependency security check..."
          if ! npm audit --audit-level=critical; then
            echo "::error::❌ Critical security vulnerabilities found in development dependencies!"
            echo "::error::📋 Run 'npm audit' locally to see details and 'npm audit fix' to resolve"
            exit 1
          fi
          
          echo "✅ Security audit passed"

      - name: Run Snyk test
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --all-projects --severity-threshold=high

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Audit Python dependencies
        run: |
          cd packages/ml
          pip install pip-audit
          pip-audit --desc

  # SAST Scanning
  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
            p/typescript
            p/react
            p/nodejs

      - name: Upload Semgrep results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: semgrep.sarif

  # License Compliance
  license-check:
    name: License Compliance Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install license checker
        run: npm install -g license-checker

      - name: Check licenses
        run: |
          # Check root
          license-checker --onlyAllow "MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;CC0-1.0;CC-BY-3.0;CC-BY-4.0;Unlicense;WTFPL" > license-report.txt
          
          # Check frontend
          cd packages/frontend
          license-checker --onlyAllow "MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;CC0-1.0;CC-BY-3.0;CC-BY-4.0;Unlicense;WTFPL" >> ../../license-report.txt
          
          # Check backend
          cd ../backend
          license-checker --onlyAllow "MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;CC0-1.0;CC-BY-3.0;CC-BY-4.0;Unlicense;WTFPL" >> ../../license-report.txt

      - name: Upload license report
        uses: actions/upload-artifact@v3
        with:
          name: license-report
          path: license-report.txt

  # Infrastructure Security
  infra-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Checkov
        uses: bridgecrewio/checkov-action@master
        with:
          directory: .
          quiet: true
          soft_fail: false
          framework: dockerfile,yaml
          output_format: sarif
          output_file_path: checkov.sarif

      - name: Upload Checkov results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: checkov.sarif

  # Comprehensive Security Enforcement
  security-enforcement:
    name: Security Enforcement Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run unified security scanner
        run: |
          chmod +x scripts/security-check.js
          npm run security:check
        env:
          NODE_ENV: production

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: security-reports/
          retention-days: 30

      - name: Security gate enforcement
        run: |
          echo "🛡️  Security enforcement completed"
          echo "✅ All security checks must pass - NO BYPASS ALLOWED"

  # Security Report
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [codeql, container-scan, secret-scan, dependency-scan, sast-scan, license-check, infra-scan, security-enforcement]
    if: always()
    steps:
      - name: Generate summary
        run: |
          echo "# Security Scan Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## Scan Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| CodeQL | ${{ needs.codeql.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Container Scan | ${{ needs.container-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Secret Scan | ${{ needs.secret-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Scan | ${{ needs.dependency-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| SAST Scan | ${{ needs.sast-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| License Check | ${{ needs.license-check.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Infrastructure Scan | ${{ needs.infra-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Enforcement | ${{ needs.security-enforcement.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🛡️ **Security Policy**: NO vulnerabilities bypass allowed" >> $GITHUB_STEP_SUMMARY
          echo "⚠️ **Critical/High vulnerabilities MUST be fixed before merge**" >> $GITHUB_STEP_SUMMARY

      - name: Notify on failure
        if: contains(needs.*.result, 'failure')
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: 'Security scan failed! Check the results.'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}