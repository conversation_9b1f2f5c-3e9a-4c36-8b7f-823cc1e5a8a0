name: Security Gate

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]
  workflow_dispatch:

permissions:
  contents: read
  security-events: write
  pull-requests: write

jobs:
  # Security Gate - MUST pass before any build/deploy
  security-gate:
    name: 🛡️ Security Gate Enforcement
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Security Gate - NPM Audit (STRICT)
        run: |
          echo "🔍 Running STRICT npm audit check..."
          echo "⚠️ NO BYPASS ALLOWED - Security vulnerabilities MUST be fixed"
          
          # Production dependencies - ZERO tolerance for high/critical
          if ! npm audit --production --audit-level=high; then
            echo "::error::❌ SECURITY GATE FAILED: Critical/High vulnerabilities in production dependencies"
            echo "::error::🚨 ACTION REQUIRED: Run 'npm audit fix' to resolve vulnerabilities"
            echo "::error::📋 For manual review: Run 'npm audit --production' to see details"
            exit 1
          fi
          
          # All dependencies - ZERO tolerance for critical
          if ! npm audit --audit-level=critical; then
            echo "::error::❌ SECURITY GATE FAILED: Critical vulnerabilities found"
            echo "::error::🚨 ACTION REQUIRED: Run 'npm audit fix' to resolve critical vulnerabilities"
            exit 1
          fi
          
          echo "✅ Security Gate: NPM audit passed"

      - name: Security Gate - Secret Detection
        run: |
          echo "🔐 Running secret detection scan..."
          
          # Install detect-secrets if not available
          pip install detect-secrets
          
          # Run secret scan
          if ! detect-secrets scan --all-files --baseline .secrets.baseline --disable-plugin AbsolutePathDetectorPlugin; then
            echo "::error::❌ SECURITY GATE FAILED: Potential secrets detected"
            echo "::error::🚨 ACTION REQUIRED: Review flagged secrets and update .secrets.baseline if they are false positives"
            echo "::error::📋 Run 'detect-secrets scan' locally to investigate"
            exit 1
          fi
          
          echo "✅ Security Gate: Secret scan passed"

      - name: Security Gate - Security Linting
        run: |
          echo "🛡️ Running security linting..."
          
          if ! npm run lint; then
            echo "::error::❌ SECURITY GATE FAILED: Security linting issues found"
            echo "::error::🚨 ACTION REQUIRED: Run 'npm run lint:fix' to auto-fix issues"
            echo "::error::📋 Review linting output above for security-related issues"
            exit 1
          fi
          
          echo "✅ Security Gate: Security linting passed"

      - name: Security Gate - Unified Security Check
        run: |
          echo "🔍 Running comprehensive security scan..."
          chmod +x scripts/security-check.js
          
          if ! npm run security:check; then
            echo "::error::❌ SECURITY GATE FAILED: Comprehensive security scan failed"
            echo "::error::🚨 ACTION REQUIRED: Check security scan output and resolve all issues"
            echo "::error::📋 Security reports saved in security-reports/ directory"
            exit 1
          fi
          
          echo "✅ Security Gate: Comprehensive security scan passed"

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-gate-reports-${{ github.run_number }}
          path: security-reports/
          retention-days: 90

      - name: Security Gate Summary
        run: |
          echo "# 🛡️ Security Gate Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## ✅ All Security Checks Passed!" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Checks Performed:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ NPM Audit (production deps: high/critical = 0)" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ NPM Audit (all deps: critical = 0)" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Secret Detection Scan" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Security Linting (ESLint security rules)" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Unified Security Scanner" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🚨 Security Policy Enforcement" >> $GITHUB_STEP_SUMMARY
          echo "- **NO bypass mechanisms allowed**" >> $GITHUB_STEP_SUMMARY
          echo "- **ALL security checks MUST pass**" >> $GITHUB_STEP_SUMMARY
          echo "- **Critical/High vulnerabilities MUST be fixed**" >> $GITHUB_STEP_SUMMARY
          echo "- **Security gate blocks all builds/deployments**" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🎉 **Ready for build and deployment!**" >> $GITHUB_STEP_SUMMARY

  # Fail-safe job to block any workflow that bypasses security
  security-enforcement:
    name: 🚨 Security Enforcement
    runs-on: ubuntu-latest
    needs: [security-gate]
    if: always()
    steps:
      - name: Enforce security gate
        run: |
          if [ "${{ needs.security-gate.result }}" != "success" ]; then
            echo "::error::❌ SECURITY ENFORCEMENT: Security gate failed"
            echo "::error::🚨 WORKFLOW BLOCKED: Cannot proceed with failed security checks"
            echo "::error::📋 Fix all security issues before proceeding"
            exit 1
          fi
          
          echo "✅ Security enforcement: All security checks passed"
          echo "🎯 Workflow can proceed to build/deploy stages"

  # Status check for branch protection
  security-status:
    name: 🔒 Security Status Check
    runs-on: ubuntu-latest
    needs: [security-gate, security-enforcement]
    if: always()
    steps:
      - name: Security status
        run: |
          if [ "${{ needs.security-gate.result }}" == "success" ] && [ "${{ needs.security-enforcement.result }}" == "success" ]; then
            echo "✅ SECURITY STATUS: PASSED"
            echo "🛡️ All security requirements met"
            exit 0
          else
            echo "❌ SECURITY STATUS: FAILED"
            echo "🚨 Security requirements not met - BLOCKED"
            exit 1
          fi