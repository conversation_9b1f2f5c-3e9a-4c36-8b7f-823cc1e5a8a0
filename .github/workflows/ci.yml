name: CI/CD Pipeline

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

env:
  NODE_VERSION: '20'

jobs:
  # Code quality checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Check formatting
        run: npm run format:check

  # Build job
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: quality
    strategy:
      matrix:
        package: [backend, frontend, shared]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build ${{ matrix.package }}
        run: npx turbo build --filter=@spheroseg/${{ matrix.package }}
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.package }}-build
          path: packages/${{ matrix.package }}/dist

  # Test job
  test:
    name: Test
    runs-on: ubuntu-latest
    needs: build
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: spheroseg_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
          
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Setup test environment
        run: |
          mkdir -p packages/backend/keys
          openssl genrsa -out packages/backend/keys/private.key 2048
          openssl rsa -in packages/backend/keys/private.key -pubout -out packages/backend/keys/public.key
          
      - name: Setup Prisma
        run: |
          cd packages/backend
          npx prisma generate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/spheroseg_test
          
      - name: Run backend tests
        run: cd packages/backend && npm test
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/spheroseg_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
          
      - name: Run frontend tests
        run: cd packages/frontend && npm test
        env:
          NODE_ENV: test

  # Docker build
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [test]
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Build Backend Docker image
        run: |
          cd packages/backend
          docker build -f Dockerfile.production -t spheroseg-backend:latest .
          
      - name: Build Frontend Docker image  
        run: |
          cd packages/frontend
          docker build -f Dockerfile -t spheroseg-frontend:latest .

  # Security scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Run npm audit
        run: npm audit --audit-level=high
        continue-on-error: true