{
  "compilerOptions": {
    // Target and module settings
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    
    // Output settings
    "outDir": "dist",
    "rootDir": "src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    
    // Type checking - STRICT MODE
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": true,
    
    // Additional strict checks
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    
    // Build enforcement - CRITICAL
    "noEmitOnError": true,
    "skipLibCheck": false,
    
    // Incremental compilation
    "incremental": true,
    "tsBuildInfoFile": ".cache/tsconfig.strict.tsbuildinfo",
    
    // Import settings
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    
    // JSX settings (for frontend)
    "jsx": "react-jsx",
    "jsxImportSource": "react"
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.test.tsx", 
    "**/*.spec.ts",
    "**/*.spec.tsx",
    "**/__tests__/**",
    "**/tests/**"
  ]
}