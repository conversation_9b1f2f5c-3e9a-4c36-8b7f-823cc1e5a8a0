#!/bin/sh

# Comprehensive pre-commit security and quality checks
set -e

echo "🔍 Running pre-commit security checks..."

# 1. Security audit check - fail on high/critical vulnerabilities
echo "📋 Running npm audit (security vulnerabilities)..."
if ! npm audit --audit-level=high; then
  echo "❌ High or critical security vulnerabilities found!"
  echo "ℹ️  Run 'npm audit fix' to resolve, or 'npm audit' for details"
  exit 1
fi

# 2. Secret detection
echo "🔐 Checking for secrets..."
if command -v detect-secrets >/dev/null 2>&1; then
  if ! detect-secrets scan --all-files --disable-plugin AbsolutePathDetectorPlugin 2>/dev/null; then
    echo "❌ Potential secrets detected!"
    echo "ℹ️  Run 'detect-secrets scan' to see details"
    exit 1
  fi
else
  echo "⚠️  detect-secrets not installed, skipping secret scan"
fi

# 3. Security linting
echo "🛡️  Running security linting..."
if ! npm run lint; then
  echo "❌ Security linting failed!"
  echo "ℹ️  Run 'npm run lint:fix' to auto-fix issues"
  exit 1
fi

# 4. Type checking
echo "📝 Running type checks..."
if ! npm run type-check; then
  echo "❌ Type checking failed!"
  exit 1
fi

# 5. Unit tests
echo "🧪 Running unit tests..."
if ! npm run test:unit; then
  echo "❌ Unit tests failed!"
  exit 1
fi

echo "✅ All pre-commit checks passed!"
