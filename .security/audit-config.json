{"$schema": "https://json.schemastore.org/sarif-2.1.0.json", "auditConfig": {"description": "SpheroSeg Security Audit Configuration", "version": "1.0.0", "auditLevels": {"production": {"critical": "error", "high": "error", "moderate": "warning", "low": "info", "info": "info"}, "development": {"critical": "error", "high": "warning", "moderate": "info", "low": "info", "info": "info"}}, "thresholds": {"production": {"maxCritical": 0, "maxHigh": 0, "maxModerate": 5, "maxLow": 20}, "development": {"maxCritical": 0, "maxHigh": 3, "maxModerate": 10, "maxLow": 50}}, "allowedVulnerabilities": {"description": "Vulnerabilities that are explicitly allowed with justification", "vulnerabilities": [{"advisory": "example-1234", "package": "example-package", "version": "1.0.0", "severity": "moderate", "justification": "False positive - not applicable to our use case", "expires": "2025-12-31", "reviewer": "security-team"}]}, "securityRules": {"eslint": {"security/detect-unsafe-regex": "error", "security/detect-buffer-noassert": "error", "security/detect-child-process": "warning", "security/detect-disable-mustache-escape": "error", "security/detect-eval-with-expression": "error", "security/detect-no-csrf-before-method-override": "error", "security/detect-non-literal-fs-filename": "warning", "security/detect-non-literal-regexp": "warning", "security/detect-non-literal-require": "warning", "security/detect-object-injection": "error", "security/detect-possible-timing-attacks": "warning", "security/detect-pseudoRandomBytes": "error", "security/detect-bidi-characters": "error"}}, "reportingSettings": {"outputDirectory": "./security-reports", "formats": ["json", "sarif"], "includeFixed": false, "includeDev": true, "retention": {"days": 90, "maxReports": 100}}, "notifications": {"onCritical": {"enabled": true, "channels": ["console", "file"]}, "onHigh": {"enabled": true, "channels": ["console", "file"]}, "onBuildFailure": {"enabled": true, "channels": ["console", "file", "ci"]}}, "integrations": {"github": {"createIssues": false, "assignees": [], "labels": ["security", "vulnerability"]}, "slack": {"enabled": false, "webhook": null, "channel": "#security-alerts"}}}, "secretsScanConfig": {"plugins": ["ArtifactoryDetector", "AWSKeyDetector", "Base64HighEntropyString", "BasicAuthDetector", "CloudantDetector", "GitHubTokenDetector", "HexHighEntropyString", "IBMCloudIamDetector", "IBMCosHmacDetector", "JwtTokenDetector", "KeywordDetector", "MailchimpDetector", "PrivateKeyDetector", "SlackDetector", "SoftlayerDetector", "StripeDetector", "TwilioKeyDetector"], "excludeFiles": ["package-lock.json", "yarn.lock", "*.min.js", "*.bundle.js", "coverage/**/*", "dist/**/*", "build/**/*", "node_modules/**/*", ".git/**/*"], "excludeLines": ["integrity.*sha[0-9]+", "\"version\":", "# pragma: allowlist secret"]}}