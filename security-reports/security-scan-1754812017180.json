{"timestamp": "2025-08-10T07:46:57.180Z", "results": {"npmAudit": false, "secretScan": false, "securityLinting": false, "dependencyCheck": true}, "errors": ["NPM audit failed: Command failed: npm audit --production --audit-level=high\nnpm warn config production Use `--omit=dev` instead.\n", "Secret scan failed: Command failed: which detect-secrets", "Security linting failed: turbo 2.5.3\n\n@spheroseg/backend:lint: ERROR: command finished with error: command (/home/<USER>/spheroseg/packages/backend) /home/<USER>/.nvm/versions/node/v22.17.0/bin/npm run lint exited (2)\n@spheroseg/backend#lint: command (/home/<USER>/spheroseg/packages/backend) /home/<USER>/.nvm/versions/node/v22.17.0/bin/npm run lint exited (2)\n WARNING  no output files found for task @spheroseg/shared#lint. Please check your `outputs` key in `turbo.json`\n ERROR  run failed: command  exited (2)\n"], "warnings": [], "summary": {"totalChecks": 4, "passed": 1, "failed": 3}}