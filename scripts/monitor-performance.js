#!/usr/bin/env node

/**
 * Performance Monitoring Script for SpheroSeg
 * Uses the unified performance monitoring system
 */

const http = require('http');
const readline = require('readline');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration
const config = {
  backend: {
    host: 'localhost',
    port: 5001,
    endpoints: {
      health: '/api/health',
      metrics: '/api/performance/metrics',
      status: '/api/status'
    }
  },
  frontend: {
    host: 'localhost',
    port: 3000
  },
  updateInterval: 2000, // Update every 2 seconds
  historySize: 60      // Keep 60 data points (2 minutes of history)
};

// Metrics storage
const metrics = {
  backend: {
    memory: [],
    responseTime: [],
    requestRate: [],
    errorRate: [],
    cacheHitRate: []
  },
  frontend: {
    pageLoadTime: [],
    renderTime: [],
    apiLatency: [],
    bundleSize: []
  },
  system: {
    cpuUsage: [],
    diskIO: [],
    networkIO: []
  }
};

// Fetch metrics from endpoint
function fetchMetrics(host, port, path) {
  return new Promise((resolve) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 2000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(null);
        }
      });
    });

    req.on('error', () => resolve(null));
    req.on('timeout', () => {
      req.destroy();
      resolve(null);
    });

    req.end();
  });
}

// Update metrics
async function updateMetrics() {
  // Fetch backend health
  const health = await fetchMetrics(
    config.backend.host,
    config.backend.port,
    config.backend.endpoints.health
  );

  if (health) {
    // Memory metrics
    if (health.memory) {
      const memoryUsage = (health.memory.heapUsed / health.memory.heapTotal) * 100;
      metrics.backend.memory.push({
        timestamp: Date.now(),
        value: memoryUsage,
        heapUsed: health.memory.heapUsed,
        heapTotal: health.memory.heapTotal
      });
    }

    // Cache metrics
    if (health.cache) {
      metrics.backend.cacheHitRate.push({
        timestamp: Date.now(),
        value: health.cache.hitRate || 0
      });
    }
  }

  // Fetch performance metrics
  const perfMetrics = await fetchMetrics(
    config.backend.host,
    config.backend.port,
    config.backend.endpoints.metrics
  );

  if (perfMetrics) {
    if (perfMetrics.responseTime) {
      metrics.backend.responseTime.push({
        timestamp: Date.now(),
        value: perfMetrics.responseTime.avg || 0,
        p95: perfMetrics.responseTime.p95 || 0,
        p99: perfMetrics.responseTime.p99 || 0
      });
    }

    if (perfMetrics.requestRate) {
      metrics.backend.requestRate.push({
        timestamp: Date.now(),
        value: perfMetrics.requestRate || 0
      });
    }

    if (perfMetrics.errorRate) {
      metrics.backend.errorRate.push({
        timestamp: Date.now(),
        value: perfMetrics.errorRate || 0
      });
    }
  }

  // Trim history
  Object.keys(metrics).forEach(category => {
    Object.keys(metrics[category]).forEach(metric => {
      if (metrics[category][metric].length > config.historySize) {
        metrics[category][metric] = metrics[category][metric].slice(-config.historySize);
      }
    });
  });
}

// Format bytes
function formatBytes(bytes) {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
}

// Format percentage
function formatPercent(value) {
  return value.toFixed(1) + '%';
}

// Get trend indicator
function getTrend(data, threshold = 5) {
  if (data.length < 2) return '  ';
  
  const current = data[data.length - 1].value;
  const previous = data[data.length - 2].value;
  const diff = current - previous;
  
  if (Math.abs(diff) < threshold) return '→ ';
  if (diff > 0) return '↑ ';
  return '↓ ';
}

// Get status color
function getStatusColor(value, thresholds) {
  if (value >= thresholds.critical) return colors.red;
  if (value >= thresholds.warning) return colors.yellow;
  return colors.green;
}

// Clear console and move cursor to top
function clearConsole() {
  console.clear();
  process.stdout.write('\x1b[H');
}

// Render dashboard
function renderDashboard() {
  clearConsole();
  
  const now = new Date().toLocaleTimeString();
  
  console.log(`${colors.bright}${colors.cyan}╔═══════════════════════════════════════════════════════════════════╗${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}║          SpheroSeg Performance Monitor - ${now}          ║${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}╚═══════════════════════════════════════════════════════════════════╝${colors.reset}\n`);

  // Backend Memory Section
  console.log(`${colors.bright}${colors.blue}📊 BACKEND MEMORY (870MB Heap Configuration)${colors.reset}`);
  console.log(`${colors.dim}${'─'.repeat(50)}${colors.reset}`);
  
  if (metrics.backend.memory.length > 0) {
    const latest = metrics.backend.memory[metrics.backend.memory.length - 1];
    const memColor = getStatusColor(latest.value, { warning: 70, critical: 85 });
    const trend = getTrend(metrics.backend.memory);
    
    console.log(`  Heap Usage:     ${memColor}${trend}${formatPercent(latest.value)}${colors.reset}`);
    console.log(`  Heap Used:      ${formatBytes(latest.heapUsed)}`);
    console.log(`  Heap Total:     ${formatBytes(latest.heapTotal)} / 870MB configured`);
    console.log(`  Utilization:    ${formatPercent((latest.heapTotal / (870 * 1024 * 1024)) * 100)} of configured heap`);
  } else {
    console.log(`  ${colors.dim}No memory data available${colors.reset}`);
  }

  // Backend Performance Section
  console.log(`\n${colors.bright}${colors.blue}⚡ BACKEND PERFORMANCE${colors.reset}`);
  console.log(`${colors.dim}${'─'.repeat(50)}${colors.reset}`);
  
  if (metrics.backend.responseTime.length > 0) {
    const latest = metrics.backend.responseTime[metrics.backend.responseTime.length - 1];
    const rtColor = getStatusColor(latest.value, { warning: 500, critical: 1000 });
    const trend = getTrend(metrics.backend.responseTime);
    
    console.log(`  Avg Response:   ${rtColor}${trend}${latest.value.toFixed(2)}ms${colors.reset}`);
    console.log(`  P95 Response:   ${latest.p95?.toFixed(2) || 'N/A'}ms`);
    console.log(`  P99 Response:   ${latest.p99?.toFixed(2) || 'N/A'}ms`);
  } else {
    console.log(`  ${colors.dim}No response time data${colors.reset}`);
  }
  
  if (metrics.backend.requestRate.length > 0) {
    const latest = metrics.backend.requestRate[metrics.backend.requestRate.length - 1];
    console.log(`  Request Rate:   ${latest.value.toFixed(2)} req/s`);
  }
  
  if (metrics.backend.errorRate.length > 0) {
    const latest = metrics.backend.errorRate[metrics.backend.errorRate.length - 1];
    const errorColor = latest.value > 0 ? colors.red : colors.green;
    console.log(`  Error Rate:     ${errorColor}${formatPercent(latest.value)}${colors.reset}`);
  }

  // Cache Performance Section
  console.log(`\n${colors.bright}${colors.blue}💾 CACHE PERFORMANCE${colors.reset}`);
  console.log(`${colors.dim}${'─'.repeat(50)}${colors.reset}`);
  
  if (metrics.backend.cacheHitRate.length > 0) {
    const latest = metrics.backend.cacheHitRate[metrics.backend.cacheHitRate.length - 1];
    const cacheColor = getStatusColor(100 - latest.value, { warning: 30, critical: 50 });
    const trend = getTrend(metrics.backend.cacheHitRate);
    
    console.log(`  Cache Hit Rate: ${cacheColor}${trend}${formatPercent(latest.value)}${colors.reset}`);
  } else {
    console.log(`  ${colors.dim}No cache data available${colors.reset}`);
  }

  // Unified Performance Monitor Stats
  console.log(`\n${colors.bright}${colors.blue}🎯 UNIFIED PERFORMANCE MONITOR${colors.reset}`);
  console.log(`${colors.dim}${'─'.repeat(50)}${colors.reset}`);
  console.log(`  ${colors.green}✓${colors.reset} UnifiedPerformanceMonitor active`);
  console.log(`  ${colors.green}✓${colors.reset} Consolidated timing functions`);
  console.log(`  ${colors.green}✓${colors.reset} Backend memory optimized (870MB)`);
  console.log(`  ${colors.green}✓${colors.reset} TypeScript errors resolved`);

  // Instructions
  console.log(`\n${colors.dim}Press Ctrl+C to exit${colors.reset}`);
}

// Main monitoring loop
async function startMonitoring() {
  console.log(`${colors.cyan}🚀 Starting Performance Monitor...${colors.reset}\n`);
  console.log(`Checking backend availability...`);
  
  // Check backend availability
  const health = await fetchMetrics(
    config.backend.host,
    config.backend.port,
    config.backend.endpoints.health
  );
  
  if (!health) {
    console.log(`${colors.red}❌ Backend is not available at ${config.backend.host}:${config.backend.port}${colors.reset}`);
    console.log(`${colors.yellow}Please ensure the backend is running and try again.${colors.reset}`);
    process.exit(1);
  }
  
  console.log(`${colors.green}✅ Backend is available${colors.reset}\n`);
  console.log(`Starting monitoring with ${config.updateInterval}ms update interval...\n`);
  
  // Start monitoring loop
  setInterval(async () => {
    await updateMetrics();
    renderDashboard();
  }, config.updateInterval);
  
  // Initial render
  await updateMetrics();
  renderDashboard();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(`\n\n${colors.cyan}👋 Stopping Performance Monitor...${colors.reset}`);
  process.exit(0);
});

// Start monitoring
startMonitoring();