#!/usr/bin/env node

/**
 * Migration script to update imports to use new modular structure
 * 
 * This script helps migrate from direct service imports to modular imports:
 * - '../services/authService' → '@spheroseg/core/auth'
 * - '../services/segmentationService' → '@spheroseg/core/segmentation'
 */

const fs = require('fs');
const path = require('path');

// Migration mappings
const IMPORT_MIGRATIONS = {
  // Auth service migrations
  "../services/authService": "@spheroseg/core/auth",
  "../../services/authService": "@spheroseg/core/auth",
  "../../../services/authService": "@spheroseg/core/auth",
  "../services/tokenService": "@spheroseg/core/auth",
  "../../services/tokenService": "@spheroseg/core/auth",
  "../middleware/auth": "@spheroseg/core/auth",
  "../../middleware/auth": "@spheroseg/core/auth",
  
  // Segmentation service migrations
  "../services/segmentationService": "@spheroseg/core/segmentation",
  "../../services/segmentationService": "@spheroseg/core/segmentation",
  "../services/segmentationQueueService": "@spheroseg/core/segmentation",
  
  // Shared kernel migrations
  "../utils/errors": "@spheroseg/shared-kernel",
  "../../utils/errors": "@spheroseg/shared-kernel",
  "../utils/ApiError": "@spheroseg/shared-kernel",
  "../../utils/ApiError": "@spheroseg/shared-kernel",
};

// Named import migrations (for specific exports)
const NAMED_IMPORT_MIGRATIONS = {
  // Auth imports
  "authService": "AuthService",
  "tokenService": "TokenService",
  "authenticate": "AuthMiddleware",
  
  // Segmentation imports
  "segmentationService": "SegmentationService",
  "segmentationQueueService": "SegmentationService",
  
  // Error imports
  "ApiError": "BaseError",
};

/**
 * Find all TypeScript/JavaScript files in a directory
 */
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function walk(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules and dist directories
        if (!['node_modules', 'dist', '.git'].includes(entry.name)) {
          walk(fullPath);
        }
      } else if (extensions.some(ext => entry.name.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  walk(dir);
  return files;
}

/**
 * Update imports in a file
 */
function updateFileImports(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let hasChanges = false;
  
  // Update import statements
  for (const [oldImport, newImport] of Object.entries(IMPORT_MIGRATIONS)) {
    const importRegex = new RegExp(
      `(import\\s*{[^}]*}\\s*from\\s*['"])${oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(['"])`,
      'g'
    );
    
    const defaultImportRegex = new RegExp(
      `(import\\s+\\w+\\s+from\\s*['"])${oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(['"])`,
      'g'
    );
    
    if (importRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(importRegex, `$1${newImport}$2`);
      hasChanges = true;
      console.log(`  ✓ Updated named import: ${oldImport} → ${newImport}`);
    }
    
    if (defaultImportRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(defaultImportRegex, `$1${newImport}$2`);
      hasChanges = true;
      console.log(`  ✓ Updated default import: ${oldImport} → ${newImport}`);
    }
  }
  
  // Update named imports within import statements
  for (const [oldName, newName] of Object.entries(NAMED_IMPORT_MIGRATIONS)) {
    const namedImportRegex = new RegExp(`\\b${oldName}\\b(?=.*from.*@spheroseg)`, 'g');
    
    if (namedImportRegex.test(updatedContent)) {
      updatedContent = updatedContent.replace(namedImportRegex, newName);
      hasChanges = true;
      console.log(`  ✓ Updated named import: ${oldName} → ${newName}`);
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent);
    console.log(`✓ Updated: ${filePath}`);
    return true;
  }
  
  return false;
}

/**
 * Main migration function
 */
function migrateImports(rootDir) {
  console.log('🚀 Starting import migration to modular structure...\n');
  
  // Focus on backend package initially
  const backendDir = path.join(rootDir, 'packages', 'backend', 'src');
  const frontendDir = path.join(rootDir, 'packages', 'frontend', 'src');
  
  let totalFiles = 0;
  let updatedFiles = 0;
  
  // Migrate backend files
  if (fs.existsSync(backendDir)) {
    console.log('📁 Migrating backend files...');
    const backendFiles = findFiles(backendDir);
    
    for (const file of backendFiles) {
      totalFiles++;
      if (updateFileImports(file)) {
        updatedFiles++;
      }
    }
  }
  
  // Migrate frontend files (less likely to need changes initially)
  if (fs.existsSync(frontendDir)) {
    console.log('\n📁 Migrating frontend files...');
    const frontendFiles = findFiles(frontendDir);
    
    for (const file of frontendFiles) {
      totalFiles++;
      if (updateFileImports(file)) {
        updatedFiles++;
      }
    }
  }
  
  console.log(`\n✨ Migration complete!`);
  console.log(`   Files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  
  if (updatedFiles > 0) {
    console.log(`\n⚠️  Next steps:`);
    console.log(`   1. Update package.json dependencies to include new modules`);
    console.log(`   2. Set up dependency injection in application startup`);
    console.log(`   3. Test the application to ensure imports work correctly`);
    console.log(`   4. Remove old service files once migration is complete`);
  }
}

// Run migration
const rootDir = process.cwd();
migrateImports(rootDir);