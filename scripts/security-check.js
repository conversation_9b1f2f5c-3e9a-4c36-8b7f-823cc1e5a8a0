#!/usr/bin/env node

/**
 * Unified Security Scanner for SpheroSeg
 * 
 * Performs comprehensive security checks including:
 * - NPM audit for vulnerabilities
 * - Secret detection
 * - Security linting
 * - Dependency validation
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

class SecurityScanner {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.results = {
      npmAudit: false,
      secretScan: false,
      securityLinting: false,
      dependencyCheck: false
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}]`;
    
    switch (type) {
      case 'error':
        console.error(chalk.red(`${prefix} ❌ ${message}`));
        break;
      case 'warning':
        console.warn(chalk.yellow(`${prefix} ⚠️  ${message}`));
        break;
      case 'success':
        console.log(chalk.green(`${prefix} ✅ ${message}`));
        break;
      case 'info':
      default:
        console.log(chalk.blue(`${prefix} 🔍 ${message}`));
        break;
    }
  }

  async runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const result = execSync(command, { 
          encoding: 'utf8', 
          stdio: 'pipe',
          ...options 
        });
        resolve(result);
      } catch (error) {
        reject({
          code: error.status,
          stdout: error.stdout || '',
          stderr: error.stderr || '',
          message: error.message
        });
      }
    });
  }

  async npmAudit() {
    this.log('Running NPM security audit...');
    
    try {
      // Check production dependencies
      await this.runCommand('npm audit --production --audit-level=high');
      
      // Generate detailed report
      const auditReport = await this.runCommand('npm audit --json --production');
      const report = JSON.parse(auditReport);
      
      if (report.metadata.vulnerabilities.high > 0 || report.metadata.vulnerabilities.critical > 0) {
        this.errors.push(`Found ${report.metadata.vulnerabilities.high} high and ${report.metadata.vulnerabilities.critical} critical vulnerabilities in production dependencies`);
        this.results.npmAudit = false;
        
        // Save detailed report
        fs.writeFileSync('./security-reports/npm-audit-report.json', JSON.stringify(report, null, 2));
        this.log('Detailed audit report saved to security-reports/npm-audit-report.json', 'info');
        
        return false;
      }
      
      this.log('No critical or high severity vulnerabilities found in production dependencies');
      this.results.npmAudit = true;
      return true;
      
    } catch (error) {
      if (error.code === 0) {
        this.results.npmAudit = true;
        return true;
      }
      
      this.errors.push(`NPM audit failed: ${error.message}`);
      this.results.npmAudit = false;
      return false;
    }
  }

  async secretScan() {
    this.log('Scanning for secrets...');
    
    try {
      // Check if detect-secrets is available
      await this.runCommand('which detect-secrets');
      
      // Run detect-secrets scan
      await this.runCommand('detect-secrets scan --all-files --disable-plugin AbsolutePathDetectorPlugin --baseline .secrets.baseline');
      
      this.log('No secrets detected');
      this.results.secretScan = true;
      return true;
      
    } catch (error) {
      if (error.message.includes('command not found')) {
        this.warnings.push('detect-secrets not installed, skipping secret scan');
        this.log('detect-secrets not installed, install with: pip install detect-secrets', 'warning');
        this.results.secretScan = true; // Don't fail if tool is not installed
        return true;
      }
      
      this.errors.push(`Secret scan failed: ${error.stderr || error.message}`);
      this.results.secretScan = false;
      return false;
    }
  }

  async securityLinting() {
    this.log('Running security linting...');
    
    try {
      // Run ESLint with security rules
      await this.runCommand('npm run lint');
      
      this.log('Security linting passed');
      this.results.securityLinting = true;
      return true;
      
    } catch (error) {
      this.errors.push(`Security linting failed: ${error.stderr || error.message}`);
      this.log('Run "npm run lint:fix" to auto-fix issues', 'info');
      this.results.securityLinting = false;
      return false;
    }
  }

  async dependencyCheck() {
    this.log('Checking dependency integrity...');
    
    try {
      // Check for package-lock.json integrity
      if (!fs.existsSync('package-lock.json')) {
        this.warnings.push('package-lock.json not found - dependency integrity cannot be verified');
        this.results.dependencyCheck = false;
        return false;
      }
      
      // Verify dependencies are installed correctly
      await this.runCommand('npm ls --depth=0');
      
      this.log('Dependency integrity check passed');
      this.results.dependencyCheck = true;
      return true;
      
    } catch (error) {
      this.errors.push(`Dependency check failed: ${error.stderr || error.message}`);
      this.results.dependencyCheck = false;
      return false;
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      results: this.results,
      errors: this.errors,
      warnings: this.warnings,
      summary: {
        totalChecks: Object.keys(this.results).length,
        passed: Object.values(this.results).filter(r => r === true).length,
        failed: Object.values(this.results).filter(r => r === false).length
      }
    };
    
    // Ensure reports directory exists
    if (!fs.existsSync('security-reports')) {
      fs.mkdirSync('security-reports', { recursive: true });
    }
    
    const reportPath = `security-reports/security-scan-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    return { report, reportPath };
  }

  printSummary(report, reportPath) {
    console.log('\n' + '='.repeat(60));
    console.log(chalk.bold('🛡️  SECURITY SCAN SUMMARY'));
    console.log('='.repeat(60));
    
    console.log(`📊 Total Checks: ${report.summary.totalChecks}`);
    console.log(`✅ Passed: ${chalk.green(report.summary.passed)}`);
    console.log(`❌ Failed: ${chalk.red(report.summary.failed)}`);
    
    if (this.warnings.length > 0) {
      console.log(`⚠️  Warnings: ${chalk.yellow(this.warnings.length)}`);
    }
    
    console.log('\n📋 Detailed Results:');
    Object.entries(this.results).forEach(([check, passed]) => {
      const status = passed ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
      console.log(`  ${check}: ${status}`);
    });
    
    if (this.errors.length > 0) {
      console.log('\n🚨 Errors Found:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${chalk.red(error)}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${chalk.yellow(warning)}`);
      });
    }
    
    console.log(`\n📄 Full report saved to: ${reportPath}`);
    console.log('='.repeat(60));
    
    return report.summary.failed === 0;
  }

  async run() {
    this.log('Starting comprehensive security scan...');
    
    const checks = [
      { name: 'NPM Audit', fn: () => this.npmAudit() },
      { name: 'Secret Scan', fn: () => this.secretScan() },
      { name: 'Security Linting', fn: () => this.securityLinting() },
      { name: 'Dependency Check', fn: () => this.dependencyCheck() }
    ];
    
    for (const check of checks) {
      try {
        await check.fn();
      } catch (error) {
        this.errors.push(`${check.name} encountered an unexpected error: ${error.message}`);
      }
    }
    
    const { report, reportPath } = this.generateReport();
    const success = this.printSummary(report, reportPath);
    
    if (!success) {
      this.log('Security scan completed with failures', 'error');
      process.exit(1);
    }
    
    this.log('Security scan completed successfully!', 'success');
    process.exit(0);
  }
}

// CLI handling
const scanner = new SecurityScanner();

// Handle CLI arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🛡️  SpheroSeg Security Scanner

Usage: node scripts/security-check.js [options]

Options:
  --help, -h     Show this help message
  
Checks performed:
  ✓ NPM audit for security vulnerabilities
  ✓ Secret detection scan
  ✓ Security linting (ESLint security rules)
  ✓ Dependency integrity verification
  
Exit codes:
  0 - All security checks passed
  1 - One or more security checks failed
  
Reports are saved to security-reports/ directory.
`);
  process.exit(0);
}

// Run the scanner
scanner.run().catch(error => {
  console.error(chalk.red('❌ Security scanner crashed:'), error.message);
  process.exit(1);
});