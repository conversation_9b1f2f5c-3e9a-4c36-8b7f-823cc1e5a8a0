# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build outputs
dist/
build/
.turbo/
.next/
out/
.nuxt/
.output/
.cache/

# Environment variables
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output/
cypress/videos/
cypress/screenshots/
playwright-report/
test-results/
*.log

# ML model weights
*.pth
*.pth.tar
*.pt
*.onnx
*.h5
*.hdf5
*.pb
*.tflite
*.mlmodel
*.caffemodel
*.weights
*.bin
checkpoint_*/

# Database
*.sqlite
*.sqlite3
*.db

# Uploads and media
uploads/
media/
public/uploads/
public/media/

# Temporary files
tmp/
temp/
.tmp/
.temp/
*.tmp
*.temp
.cache/
cache/

# Processing files
processing/
.processing/
segmentation_temp/
ml_temp/

# Docker
.docker/
docker-volumes/

# Package manager files
package-lock.json.bak
yarn.lock.bak
.pnpm-store/
.yarn/
.npm/

# Runtime files
*.pid
*.sock
*.lock

# Backup files
*.bak
*.backup
*~
*.orig

# Cache files
.cache/
.parcel-cache/
.webpack/
.eslintcache
.stylelintcache

# TypeScript build artifacts
*.tsbuildinfo
packages/types/src/*.js
packages/types/src/*.js.map
packages/types/src/*.d.ts
!packages/types/src/index.d.ts

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

**/.claude/settings.local.json

# SSL Certificates and Security
letsencrypt/
ssl-certs/
*.key
*.crt
*.pem
*.csr
*.p12
*.pfx
certbot/
certificates/

# User data and uploads (prevent accidental commits)
user_uploads/
public/user_data/
assets/user_content/
storage/uploads/

# Generated reports
reports/
benchmarks/
performance_logs/
security-reports/

# IDE temp files
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml
packages/ml/__pycache__/
*.pyc

# Test files and outputs
test-results/
test-data/
test-*.html
test-*.json
test-*.sh
test-*.js
test-*.cjs
test-*.png
test-*.jpg
*.patch

# E2E test fixtures and results
**/e2e/fixtures/
**/playwright-report/
**/test-results/
packages/frontend/test-results/

# Test images and media files
.archive/
test-pipeline/
**/test-image*
**/test-*.bmp
**/test-*.jpg
**/test-*.png
**/test-*.tiff

# Legacy and backup files  
legacy-code-backup/
**/legacy-*
backup-*/
*.bak
*.backup

# Python cache
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.mypy_cache/

# Python virtual environments
venv/
env/
ENV/
.venv/
.env/
virtualenv/
*.virtualenv
packages/ml/venv/

# Temporary and debug files
debug-*.log
debug-*.sh
*.tmp.*
temp-*
tmp-*

# Security and credentials
cookies.txt
auth-tokens.json
credentials.json
secrets/
private/
packages/backend/keys/
**/keys/
jwt-keys/
jwt_keys/
rsa-keys/
rsa_keys/

# Deployment specific
deploy-*.sh
deployment-*.log
nginx/*.conf.backup

# Docker override files
docker-compose.override.yml
docker-compose.*.override.yml

# Backup and archive files
backup-*/
archive-*/
*.tar.gz
*.zip
*.rar

# Generated documentation
docs/generated/
api-docs/

# Local database dumps
*.sql
*.dump
db-backup/

# IDE specific files
.project
.classpath
*.iml
*.ipr
*.iws

# System files
spheroseg/

# Generated Prisma client
/generated/
packages/backend/generated/
packages/*/generated/
**/generated/prisma/

# Old and backup files
*.old.ts
*.old.js
*.old.tsx
*.old.jsx
authService.old.ts
packages/backend/packages/
